﻿@inherits LayoutComponentBase

<MudThemeProvider @bind-IsDarkMode="_isDarkMode" Theme="_theme" />
<MudDialogProvider />
<MudSnackbarProvider />

<MudLayout>
    <MudAppBar Elevation="2">
        <MudIconButton Icon="@Icons.Material.Filled.Menu" Color="Color.Inherit" Edge="Edge.Start"
            OnClick="@((e) => DrawerToggle())" />
        <MudText Typo="Typo.h5" Class="ml-3">Database Chat</MudText>
        @* <MudSpacer />
        <MudIconButton Icon="@Icons.Material.Filled.MoreVert" Color="Color.Inherit" Edge="Edge.End" /> *@
    </MudAppBar>
    <MudDrawer @bind-Open="_drawerOpen" ClipMode="DrawerClipMode.Always" Elevation="2">
        <NavMenu />

        <MudRadioGroup Class="mx-4 mt-12" @bind-Value="_isDarkMode">
            <MudRadio Value="true" Color="Color.Primary" Dense="false">Dark mode</MudRadio>
            <MudRadio Value="false" Color="Color.Secondary" Dense="false">Light mode</MudRadio>
        </MudRadioGroup>
    </MudDrawer>
    <MudMainContent>
        @Body
    </MudMainContent>
</MudLayout>
@code {
    bool _drawerOpen = true;
    private MudTheme _theme = new();
    private bool _isDarkMode = true;

    void DrawerToggle()
    {
        _drawerOpen = !_drawerOpen;
    }
}

<style>
    body {
        font-size: 1.2em !important;
    }
</style>