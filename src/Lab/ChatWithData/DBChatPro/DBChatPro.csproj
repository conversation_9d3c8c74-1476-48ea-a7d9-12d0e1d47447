﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
		<UserSecretsId>b7dd9752-9502-4111-9108-38b64831fedb</UserSecretsId>
		<DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
		<DockerfileContext>..\..\..\..</DockerfileContext>
	</PropertyGroup>



	<ItemGroup>
		<PackageReference Include="Azure.Data.Tables" Version="12.10.0" />
		<PackageReference Include="Azure.Identity" Version="1.13.2" />
		<PackageReference Include="Azure.Security.KeyVault.Secrets" Version="4.7.0" />
		<PackageReference Include="Markdig" Version="0.41.0" />
		<PackageReference Include="Microsoft.Extensions.AI" Version="9.3.0-preview.1.25161.3" />
		<PackageReference Include="Microsoft.Extensions.AI.AzureAIInference" Version="9.3.0-preview.1.25161.3" />
		<PackageReference Include="Microsoft.Extensions.AI.Ollama" Version="9.3.0-preview.1.25161.3" />
		<PackageReference Include="Microsoft.Extensions.AI.OpenAI" Version="9.3.0-preview.1.25161.3" />
		<PackageReference Include="Microsoft.Extensions.Azure" Version="1.11.0" />
		<PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.Server" Version="9.0.4" />
		<PackageReference Include="Azure.AI.OpenAI" Version="2.2.0-beta.2" />
		<PackageReference Include="Microsoft.Data.SqlClient" Version="6.0.1" />
		<PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.2" />
		<PackageReference Include="MudBlazor" Version="6.*" />
		<PackageReference Include="MySqlConnector.DependencyInjection" Version="2.4.0" />
		<PackageReference Include="Npgsql" Version="9.0.3" />
		<PackageReference Include="OpenAI" Version="2.2.0-beta.3" />
		<PackageReference Include="Oracle.ManagedDataAccess.Core" Version="23.8.0" />
		<PackageReference Include="AWSSDK.BedrockRuntime" Version="3.7.418.7" />
		<PackageReference Include="AWSSDK.Core" Version="3.7.402.43" />
		<PackageReference Include="AWSSDK.Extensions.NETCore.Setup" Version="3.7.400" />
	</ItemGroup>

</Project>
