@using System.Linq.Expressions
@using Microsoft.AspNetCore.Components.Forms
<InputSelect class="w-full mt-2 px-3 py-1 rounded-md shadow-md focus:outline focus:outline-2 focus:outline-blue-500" Value="@Value" ValueChanged="@ValueChanged" ValueExpression="@ValueExpression">
    <option value=""></option>
    <option value="@(TyreStatus.NeedsReplacement)">Needs replacement</option>
    <option value="@(TyreStatus.Worn)">Worn</option>
    <option value="@(TyreStatus.Good)">Good</option>
    <option value="@(TyreStatus.New)">New</option>
</InputSelect>
@code {
    [Parameter]
    public TyreStatus? Value { get; set; }

    [Parameter]
    public EventCallback<TyreStatus?> ValueChanged { get; set; }

    [Parameter]
    public Expression<Func<TyreStatus?>>? ValueExpression { get; set; }
}
