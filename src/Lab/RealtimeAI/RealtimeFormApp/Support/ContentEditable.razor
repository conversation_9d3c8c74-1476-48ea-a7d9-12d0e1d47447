﻿@inject IJSRuntime JS
@using RealtimeFormApp
@using Microsoft.JSInterop

<div @ref="@elem" @attributes="@AdditionalAttributes" contenteditable="plaintext-only" @bind-content:get="@Value" @bind-content:set="@(val => ValueChanged.InvokeAsync(val))"></div>

@code {
    ElementReference elem;

    [Parameter(CaptureUnmatchedValues = true)]
    public IDictionary<string, object>? AdditionalAttributes { get; set; }

    [Parameter]
    public string? Value { get; set; }

    [Parameter]
    public EventCallback<string?> ValueChanged { get; set; }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            var module = await JS.InvokeAsync<IJSObjectReference>("import", "./Support/ContentEditable.razor.js");
            await module.InvokeVoidAsync("start", elem);
        }
    }
}
