﻿using System.ComponentModel;
using System.Text.Json.Serialization;
using RealPlusNLP.Api.Common.Models;

namespace RealPlusNLP.Api.Features.QueryToSearchOptions.Models;

public sealed record SearchCriteriaNLPModel(
    ListingCategoryType ListingCategory,
    string? BuildingPeriods,
    string? OwnershipType,
    string? Amenities,
    string? AttendedLobby,
    [property: JsonPropertyName("bedrooms_min")] string? BedroomsMin,
    [property: JsonPropertyName("bedrooms_max")] string? BedroomsMax,
    [property: JsonPropertyName("bathrooms_min")] string? BathroomsMin,
    [property: JsonPropertyName("bathrooms_max")] string? BathroomsMax,
    [property: JsonPropertyName("rooms_min")] double? RoomsMin,
    [property: JsonPropertyName("rooms_max")] double? RoomsMax,
    [property: JsonPropertyName("price_min")] string? PriceMin,
    [property: <PERSON>son<PERSON>ropertyName("price_max")] string? PriceMax,
    [property: Json<PERSON>ropertyName("sqft_min")]
    [property: Description("Min apartment square feet. Should be less than or equal to sqft_max")]
    double? SqFtMin,
    [property: JsonPropertyName("sqft_max")]
    [property: Description("Max apartment square feet. Should be more than or equal to sqft_min")]
    double? SqFtMax,
    [property: Description("a list of neighborhoods (id and name)")] NeighborhoodModel[]? Neighborhoods,
    ListingStatus[]? ListingStatus,
    [property: JsonPropertyName("unprocessed_criteria")] string? UnprocessedCriteria);

[JsonConverter(typeof(JsonStringEnumConverter))]
[Description("Listing category types: Sales or Rentals. If nothing is specified set to Sales (8)")]
public enum ListingCategoryType
{
    Sales = 8,
    Rentals = 7
}

[JsonConverter(typeof(JsonStringEnumConverter))]
[Description("Status of the listing")]
public enum ListingStatus
{
    [Description("Status of the sale and rental active listings")]
    Active,
    [Description("Status of the sale contract signed listings. The status name can be shorten to CS")]
    ContractSigned,
    [Description("Status of the rental lease signed listings. The status name can be shorten to LS")]
    LeaseSigned,
    [Description("Status of the sale sold listings")]
    Sold,
    [Description("Status of the rental rented listings")]
    Rented
}