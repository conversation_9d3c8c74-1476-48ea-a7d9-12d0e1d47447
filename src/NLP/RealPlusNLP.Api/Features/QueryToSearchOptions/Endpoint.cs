﻿using Microsoft.AspNetCore.Mvc;
using RealPlusNLP.Api.Features.QueryToSearchOptions.Models;
using RealPlusNLP.Api.Common.Filters;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using System.ComponentModel;

namespace RealPlusNLP.Api.Features.QueryToSearchOptions;

public sealed class Endpoint : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        app.MapPost("listings/query-to-search-options", async (
            ISender sender,
            [FromBody] QueryToSearchOptionsApiRequest request,
            CancellationToken cancellationToken) =>
        {
            var response = await sender.Send(
                new Handler.Request(request.Query), cancellationToken);

            return TypedResults.Ok(response.Adapt<QueryToSearchOptionsApiResponse>());
        })
            .Accepts<QueryToSearchOptionsApiRequest>("application/json")
            .Produces<QueryToSearchOptionsApiResponse>(StatusCodes.Status200OK)
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .ProducesProblem(StatusCodes.Status401Unauthorized)
            .WithName("QueryToSearchOptions")
            .WithTags("ListingsSearch")
            .WithSummary("Transform a query string into search options")
            .WithDescription("Transform a query string into search options with AI")
#if !DEBUG
            .AddEndpointFilter<ApiKeyEndpointFilter>()
#endif
            .WithOpenApi();
    }
}

// api request
internal record QueryToSearchOptionsApiRequest(
    [property: Required]
    [property: Description("The query string to transform into search options")]
    string Query);

[JsonSerializable(typeof(QueryToSearchOptionsApiRequest))]
[JsonSourceGenerationOptions(GenerationMode = JsonSourceGenerationMode.Default)]
internal partial class QueryToSearchOptionsApiRequestJsonContext
    : JsonSerializerContext { }

// api response
internal record QueryToSearchOptionsApiResponse(
    [property: Description("Search options")]
    SearchCriteriaViewModel SearchOptions,
    string? UnprocessedCriteria);
[JsonSerializable(typeof(QueryToSearchOptionsApiResponse))]
[JsonSourceGenerationOptions(GenerationMode = JsonSourceGenerationMode.Default)]
internal partial class QueryToSearchOptionsApiResponseJsonContext
    : JsonSerializerContext { }
