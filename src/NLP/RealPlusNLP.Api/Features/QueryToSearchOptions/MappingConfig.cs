﻿using RealPlusNLP.Api.Features.QueryToSearchOptions.Models;

namespace RealPlusNLP.Api.Features.QueryToSearchOptions;

public sealed class MappingConfig : IRegister
{
    public void Register(TypeAdapterConfig config)
    {
        config.NewConfig<SearchCriteriaNLPModel, SearchCriteriaViewModel>()
            .Map(dest => dest.listingCategoryId, src => (int)src.ListingCategory)
            .Map(dest => dest.buildingPeriods, src => src.BuildingPeriods)
            .Map(dest => dest.ownershipType, src => src.OwnershipType)
            .Map(dest => dest.amenities, src => src.Amenities)
            .Map(dest => dest.attendedLobby, src => src.AttendedLobby)
            .Map(dest => dest.pricemin, src => src.PriceMin)
            .Map(dest => dest.pricemax, src => src.PriceMax)
            .Map(dest => dest.rooms, src => src.RoomsMin)
            .Map(dest => dest.maxrooms, src => src.RoomsMax)
            .Map(dest => dest.bathrooms, src => src.BathroomsMin)
            .Map(dest => dest.maxbathrooms, src => src.BathroomsMax)
            .Map(dest => dest.bedrooms, src => src.BedroomsMin)
            .Map(dest => dest.maxbedrooms, src => src.BedroomsMax)
            .Map(dest => dest.minSqft, src => src.SqFtMin)
            .Map(dest => dest.maxSqft, src => src.SqFtMax)
            .Map(dest => dest.areaOrNeighborhood,
                src => src.Neighborhoods == null || src.Neighborhoods.Length == 0
                ? null : string.Join(",", src.Neighborhoods.Select(x => x.Id)))
            .Map(dest => dest.tempAreaOrNeighborhoodIds,
                src => src.Neighborhoods == null || src.Neighborhoods.Length == 0
                ? null : string.Join("$$", src.Neighborhoods.Select(x => x.Id)))
            .Map(dest => dest.quickSearchInfo,
                src => src.Neighborhoods == null || src.Neighborhoods.Length == 0
                ? null : string.Join("$$", src.Neighborhoods.Select(x => $"Neighborhood: {x.Name}")))
            .Map(dest => dest.listingCategorystatus,
                src => src.ListingStatus == null || src.ListingStatus.Length == 0
                ? null : string.Join(",", GetListingStatuses(src.ListingStatus, src.ListingCategory)))
            // defaults
            .Map(dest => dest.roomsPlus, src => true)
            .Map(dest => dest.bedroomsPlus, src => true)
            .Map(dest => dest.bathroomsPlus, src => true);
    }

    static int[] GetListingStatuses(ListingStatus[] listingStatuses, ListingCategoryType listingCategory)
    {
        return [.. listingStatuses
            .Select(x => {
                return listingCategory switch
                {
                    ListingCategoryType.Sales => (int)x + 162,
                    ListingCategoryType.Rentals => (int)x + 3592,
                    _ => throw new ArgumentOutOfRangeException(nameof(listingCategory))
                };
            })];
    }
}
