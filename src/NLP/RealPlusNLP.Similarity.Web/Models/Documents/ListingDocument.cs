﻿using System.Text.Json;

namespace RealPlusNLP.Similarity.Web.Models.Documents;

public sealed record ListingDocument(
    int Id,
    string SecureGroup,
    string ListingCompany,
    string? HasCopyForCompany,
    string Status,
    string ListingNumber,
    string AsText,
    string AsJson,
    float[] TextVector)
{
    private ListingDetails _details = default!;

    public ListingDetails Details 
    {
        get 
        {
            if (!string.IsNullOrEmpty(AsJson))
            {
                _details ??= JsonSerializer.Deserialize<ListingDetails>(AsJson)!;
            }
            
            return _details ?? default!;
        }
    }
}

public sealed record ListingDetails(
    string ListingCategory,
    string ListingType,
    string OwnershipType,
    string Borough,
    string Neighborhood,
    string Address,
    string StreetName,
    string? CrossStreets,
    string? BuildingName,
    string BuildingNumber,
    string? UnitNumber,
    float? Bathrooms,
    float? Bedrooms,
    float? TotalRooms,
    int? SquareFootage,
    int Price,
    double? PricePerSqFt,
    int? PricePerRoom,
    int? MaintCC,
    int? MonthlyExpence,
    double? Longitude,
    double? Latitude,
    string? ConstructionType,
    string? CommonOutdoorSpace,
    string? BuildingFeatures,
    string? BuildingPeriod,
    string? BuildingAllows,
    string? BuildingNotAllows,
    string? ListingDescription,
    string? BuildingDescription
);