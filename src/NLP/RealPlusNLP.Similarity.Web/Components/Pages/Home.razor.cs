using Elastic.Clients.Elasticsearch;
using Elastic.Clients.Elasticsearch.Core.Search;
using Microsoft.Extensions.AI;
using MudBlazor;
using RealPlusNLP.Similarity.Web.Models.Documents;
using RealPlusNLP.Similarity.Web.Models.Views;
using System.Data;

namespace RealPlusNLP.Similarity.Web.Components.Pages;

public partial class Home(
    ElasticsearchClient elasticClient,
    IChatClient chatClient,
    ISnackbar snackbar)
{
    private MudTable<ListingViewModel> _mudTable = default!;

    private bool _similarityMode = false;
    private ListingViewModel? _sourceListing;
    private string? _sourceListingText;
    private string? _similarListingText;
    private bool _loading;
    private int _selectedRowNumber = -1;

    // AI Drawer properties
    private bool _showAiDrawer = false;
    private bool _isAiLoading = false;
    private string _aiAnalysisResult = string.Empty;

    private IEnumerable<ListingViewModel> _listings = [];
    private IEnumerable<ListingViewModel> _viewListings = [];

    private readonly ChatOptions _chatOptions = new()
    {
        ResponseFormat = ChatResponseFormat.Text,
        Temperature = 0f
    };

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await LoadListings();
        }
    }

    protected async Task LoadListings()
    {
        _loading = true;
        StateHasChanged();
        try
        {
            var listings = await elasticClient
                .SearchAsync<ListingDocument>(s => s
                    .Index("listing-similarity")
                    .Size(1000)
                    .Source(new SourceConfig(new SourceFilter
                    {
                        Excludes = new[] {
                            Infer.Field<ListingDocument>(f => f.TextVector)
                        }
                    })));

            _viewListings = _listings = [.. listings
                .Documents
                .Select(d => new ListingViewModel(d))];
        }
        finally
        {
            _loading = false;
            StateHasChanged();
        }
    }

    protected async Task FindSimilarWithVector()
    {
        if (_sourceListing is null)
        {
            snackbar.Add("Select a listing", Severity.Warning);
            return;
        }

        _similarityMode = _loading = true;

        try
        {
            var getResponse = await elasticClient
                .GetAsync<ListingDocument>(_sourceListing.Listing.Id, g => g
                    .Index("listing-similarity")
                    .SourceIncludes(new[] {
                        Infer.Field<ListingDocument>(f => f.TextVector)
                    }));

            var searchResponse = await elasticClient
                .SearchAsync<ListingDocument>(s => s
                    .Index("listing-similarity")
                    .Knn(k => k
                        .Field(f => f.TextVector)
                        .QueryVector(getResponse.Source?.TextVector)
                        .k(10)
                        .NumCandidates(10 * 50)
                        .Filter(f => f
                            .Bool(b => b
                                .MustNot(m => m
                                    .Term(t => t
                                        .Field(f => f.Id)
                                        .Value(_sourceListing.Listing.Id))
                                )
                            )
                        )
                    )
                    .SourceExcludes(new[] {
                        Infer.Field<ListingDocument>(f => f.TextVector)
                    })
                    .Size(10));

            var results = new List<ListingViewModel>();
            foreach (var hit in searchResponse.Hits)
            {
                var src = hit.Source!;
                var score = hit.Score;
                results.Add(new ListingViewModel(
                    src,
                    score
                ));
            }

            _viewListings = results;
        }
        finally
        {
            _loading = false;
        }
    }

    protected async Task FindSimilarWithText()
    {
        if (_sourceListing is null)
        {
            snackbar.Add("Select a listing", Severity.Warning);
            return;
        }

        _similarityMode = _loading = true;

        try
        {
            var searchResponse = await elasticClient
                .SearchAsync<ListingDocument>(s => s
                    .Index("listing-similarity")
                    .Query(q => q
                        .Bool(b => b
                            .Must(m => m
                                .Match(m => m
                                    .Field(f => f.AsText)
                                    .Query(_sourceListingText!))
                            )
                            .MustNot(mn => mn
                                .Term(t => t
                                    .Field(f => f.Id)
                                    .Value(_sourceListing.Listing.Id))
                            )
                        )
                    )
                    .SourceExcludes(new[] {
                        Infer.Field<ListingDocument>(f => f.TextVector)
                    })
                    .Size(10));

            double maxScore = (float)searchResponse.Hits.Max(h => h.Score ?? 0);
            var results = new List<ListingViewModel>();
            foreach (var hit in searchResponse.Hits)
            {
                var src = hit.Source!;
                var score = hit.Score / maxScore;
                results.Add(new ListingViewModel(
                    src,
                    score
                ));
            }

            _viewListings = results;
        }
        finally
        {
            _loading = false;
        }
    }

    protected void Reset()
    {
        _loading = true;

        _viewListings = _listings;
        _sourceListing = null;
        _similarityMode = false;
        _selectedRowNumber = -1;
        _similarListingText = _sourceListingText = null;

        _loading = false;
    }

    private async Task AskAi()
    {
        if (_similarListingText is null)
        {
            snackbar.Add("Select a listing to compare with source listing", Severity.Warning);
            return;
        }

        // Show drawer and start loading
        _showAiDrawer = true;
        _isAiLoading = true;
        _aiAnalysisResult = string.Empty;
        StateHasChanged();

        try
        {
            //For the search I used the Elasticsearch KNN algorithm to find similar listings
            var response = chatClient.GetStreamingResponseAsync($"""
                Analyze these two property listings identified as similar by our matching system:

                Source Listing:
                {_sourceListingText}

                Matched Listing:
                {_similarListingText}

                Provide a structured comparison focusing on:

                **Direct Matches** - Specific words/phrases that appear in both listings
                **Theme Alignment** - Similar ideas/concepts (even when phrased differently)
                **Key Matching Factors** - 3-5 primary reasons the system would pair these
                **Notable Differences** - Important distinctions a user should notice
                **Confidence Assessment** - "High/Medium/Low" match confidence with brief rationale

                Present as a clear report using simple business language, avoiding technical terms about how the matching system works. Focus on practical insights for non-technical users.
            """, _chatOptions);

            await foreach (var message in response)
            {
                _aiAnalysisResult += message.Text;
                if (_isAiLoading)
                {
                    _isAiLoading = false;
                }
                StateHasChanged();
            }
        }
        catch (Exception ex)
        {
            _aiAnalysisResult = $"Error analyzing listings: {ex.Message}";
            snackbar.Add("Error analyzing listings", Severity.Error);
        }
        finally
        {
            if (_isAiLoading)
            {
                _isAiLoading = false;
                StateHasChanged();
            }
        }
    }

    private void CloseAiDrawer()
    {
        _showAiDrawer = false;
    }

    private string SelectedRowClassFunc(ListingViewModel? item, int rowNumber)
    {
        if (_selectedRowNumber == rowNumber)
        {
            _selectedRowNumber = -1;
            return string.Empty;
        }
        else if (_mudTable.SelectedItem != null && _mudTable.SelectedItem.Equals(item))
        {
            _selectedRowNumber = rowNumber;
            return "selected-row";
        }
        else
        {
            return string.Empty;
        }
    }

    private void RowClickEvent(ListingViewModel? item)
    {
        if (_similarityMode)
        {
            _similarListingText = item?.Listing.AsText;
        }
        else
        {
            _sourceListing = item;
            _sourceListingText = item?.Listing.AsText;
        }
    }
}
