@page "/"

<PageTitle>Listings Similarity</PageTitle>

@using RealPlusNLP.Similarity.Web.Models.Views

<style>
    .selected-row {
        background-color: rgba(var(--mud-palette-primary-rgb), 0.1) !important;
    }
</style>

<MudContainer MaxWidth="MaxWidth.ExtraExtraLarge" Class="mt-2" Fixed="true"
    Style="height: calc(100vh - 110px); display: flex; flex-direction: column;">

    <!-- Top block with buttons -->
    <MudPaper Elevation="1" Class="mb-3 d-flex pa-3 align-center justify-space-between" xs="8">
        <div class="d-flex align-center">
            <MudText Typo="Typo.body1" HtmlTag="strong" Class="mr-3">
                <MudIcon Icon="@Icons.Material.Filled.Search" Class="mr-2" Style="margin-bottom: -7px;" />Select a listing and click Search
            </MudText>
            @* <MudText Typo="Typo.body1" HtmlTag="strong" Class="mr-3">Select a listing and click to find similar listings with</MudText> *@
@*             <MudButton Variant="Variant.Filled" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Search"
                Size="Size.Medium" Disabled="@(_similarityMode)" Class="mr-3" OnClick="FindSimilarAsVector">
                Find Similar
            </MudButton>*@
            <MudButtonGroup Color="Color.Primary" Variant="Variant.Filled" Size="Size.Medium" Class="mr-3">
                <MudButton Disabled="@(_similarityMode)" OnClick="FindSimilarWithVector">
                    Search
                </MudButton>
                @* <MudButton Disabled="@(_similarityMode)" OnClick="FindSimilarWithText">
                    Text
                </MudButton> *@
            </MudButtonGroup>
            <MudButton Variant="Variant.Filled" Color="Color.Default" StartIcon="@Icons.Material.Filled.Clear"
                Size="Size.Medium" Disabled="@(!_similarityMode)" OnClick="Reset">
                Reset
            </MudButton>
        </div>
        @* <MudTooltip Text="Ask AI why selected listings are similar"> *@
            <MudButton Variant="Variant.Filled" Color="Color.Tertiary" StartIcon="@Icons.Material.Filled.Psychology"
                Size="Size.Medium" Class="ml-auto" Disabled="@(!_similarityMode)" OnClick="@(async () => await AskAi())">
                Ask AI
            </MudButton>
        @* </MudTooltip> *@
    </MudPaper>

    <!-- Bottom block with the result -->
    <MudTable Items="@_viewListings" @ref="_mudTable"
        OnRowClick="@((TableRowClickEventArgs<ListingViewModel> args) => RowClickEvent(args.Item))"
        RowClassFunc="@SelectedRowClassFunc" Loading="@_loading" LoadingProgressColor="Color.Info"
        Hover="true" Dense="true" Striped="false" Breakpoint="Breakpoint.Sm"
        Bordered="false" FixedHeader="true" Height="calc((100vh - 170px) * 0.5)"
        Class="mt-3" RowClass="cursor-pointer" 
        RowStyle="white-space: nowrap;">
@*         <ColGroup>
            @if (_similarityMode)
            {
                <col style="width: 30px;" />
            }
            <col style="width: 40px;" />
            <col style="width: 60%;" />
            <col />
            <col />
        </ColGroup>
 *@        <HeaderContent>
            @if (_similarityMode)
            {
                <MudTh>Similarity</MudTh>
            }
            <MudTh>Listing #</MudTh>
            <MudTh>Ownership</MudTh>
            <MudTh>Neighborhood</MudTh>
            <MudTh>Address</MudTh>
            <MudTh>Unit #</MudTh>
            <MudTh>Price</MudTh>
            <MudTh>Bds</MudTh>
            <MudTh>Bths</MudTh>
            <MudTh>SqFt</MudTh>
        </HeaderContent>
        <RowTemplate>
            @if (_similarityMode)
            {
                <MudTd DataLabel="Similarity" Style="text-align:right">@(context?.Similarity?.ToString("P2"))</MudTd>
            }
            <MudTd DataLabel="Listing #">@context?.Listing.ListingNumber</MudTd>
            <MudTd DataLabel="Ownership">@context?.Listing.Details.OwnershipType</MudTd>
            <MudTd DataLabel="Neighborhood">@context?.Listing.Details.Neighborhood</MudTd>
            <MudTd DataLabel="Address">@context?.Listing.Details.Address</MudTd>
            <MudTd DataLabel="Unit #">@context?.Listing.Details.UnitNumber</MudTd>
            <MudTd DataLabel="Price" Style="text-align:right">@context?.Listing.Details.Price.ToString("C0", new System.Globalization.CultureInfo("en-US"))</MudTd>
            <MudTd DataLabel="Bds" Style="text-align:right">@context?.Listing.Details.Bedrooms?.ToString("N0")</MudTd>
            <MudTd DataLabel="Bths" Style="text-align:right">@context?.Listing.Details.Bathrooms?.ToString("N1")</MudTd>
            <MudTd DataLabel="SqFt" Style="text-align:right">@context?.Listing.Details.SquareFootage?.ToString("N0")</MudTd>
        </RowTemplate>
    </MudTable>

    <!-- Details Panel for Selected Listings -->
    <MudPaper Elevation="1" Class="mt-3" Style="height: calc((100vh - 170px) * 0.5 - 16px); display: flex;">
        <!-- Source Listing Details -->
        <MudItem Class="pa-3" Style="width: 50%; overflow-y: auto;">
            <MudText Typo="Typo.subtitle2" Class="mb-2">Source Listing</MudText>
            <MudText Typo="Typo.body2" Style="white-space: pre-wrap;">@_sourceListingText</MudText>
        </MudItem>
        <!-- Vertical Divider -->
        <MudDivider Vertical="true" FlexItem="true" />
        <!-- Similar Listing Details -->
        <MudItem Class="pa-3" Style="width: 50%; overflow-y: auto;">
            <MudText Typo="Typo.subtitle2" Class="mb-2">Similar Listing</MudText>
            <MudText Typo="Typo.body2" Style="white-space: pre-wrap;">@_similarListingText</MudText>
        </MudItem>
    </MudPaper>
</MudContainer>

<!-- AI Analysis Drawer -->
<MudDrawer @bind-Open="_showAiDrawer" Anchor="Anchor.Bottom" Elevation="1"
    Variant="DrawerVariant.Temporary" Height="90%" OverlayAutoClose="false">
    <MudDrawerHeader>
        <MudText Typo="Typo.h6">
            <MudIcon Icon="@Icons.Material.Filled.Psychology" Class="mr-2" Style="margin-bottom: -7px;" />AI Similarity Analysis
        </MudText>
        <MudSpacer />
        <MudIconButton Icon="@Icons.Material.Filled.Close" Color="Color.Default"
            OnClick="CloseAiDrawer" Disabled="@_isAiLoading" aria-label="Close" />
    </MudDrawerHeader>
    <MudDrawerContainer Style="height: calc(100% - 64px);">
        @if (_isAiLoading)
        {
            <div class="d-flex flex-column align-center justify-center" style="height: 50vh;">
                <MudProgressCircular Color="Color.Tertiary" Size="Size.Large" Indeterminate="true" />
                <MudText Typo="Typo.body1" Class="mt-4">Analyzing selected listings similarity...</MudText>
            </div>
        }
        else
        {
            <div class="pa-4" style="overflow-y: auto; height: 100%; max-height: calc(80vh - 80px);">
                <MudMarkdown Value="@_aiAnalysisResult" />
@*                 <MudText Style="white-space: pre-wrap; font-family: monospace;">@_aiAnalysisResult</MudText>
 *@            </div>
        }
    </MudDrawerContainer>
</MudDrawer>
