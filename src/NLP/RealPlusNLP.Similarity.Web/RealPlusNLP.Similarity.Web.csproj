﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>cfb8efbd-5496-4056-a67e-cd6aa437b62d</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerfileContext>..\..\..</DockerfileContext>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.2" />
    <PackageReference Include="MudBlazor" Version="8.6.0" />
    <PackageReference Include="Elastic.Clients.Elasticsearch" Version="8.18.0" />
	<PackageReference Include="Azure.AI.OpenAI" Version="2.1.0" />
	<PackageReference Include="Microsoft.Extensions.AI" Version="9.5.0-preview.1.25262.9" />
	<PackageReference Include="Microsoft.Extensions.AI.OpenAI" Version="9.5.0-preview.1.25262.9" />
	<PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="9.0.5" />
	<PackageReference Include="Azure.Identity" Version="1.14.0" />
	<PackageReference Include="MudBlazor.Markdown" Version="8.6.0" />
  </ItemGroup>
</Project>