using Azure.AI.OpenAI;
using Azure.Identity;
using Elastic.Clients.Elasticsearch;
using Elastic.Transport;
using Microsoft.Extensions.AI;
using MudBlazor;
using MudBlazor.Services;
using RealPlusNLP.Similarity.Web.Components;
using RealPlusNLP.Similarity.Web.Models.Documents;

var builder = WebApplication.CreateBuilder(args);

// Add MudBlazor services
builder.Services.AddMudServices();
builder.Services.AddMudMarkdownServices();

builder.Services.AddSingleton(sp =>
{
    var settings = new ElasticsearchClientSettings(new Uri("https://rpls-dev-vector.es.eastus2.azure.elastic-cloud.com"))
        .Authentication(new BasicAuthentication("elastic", "Npgpn8W2uFC8N2myxbWSWacA"))
        .DefaultMappingFor<ListingDocument>(m => m
            .IndexName("listing-similarity")
            .IdProperty(p => p.Id));

    return new ElasticsearchClient(settings);
});
builder.Services.AddChatClient(
    new AzureOpenAIClient(
        new Uri("https://ai-foundry-dev.cognitiveservices.azure.com/"),
        new DefaultAzureCredential())
    .GetChatClient("gpt-4o").AsIChatClient());

// Add services to the container.
builder.Services.AddRazorComponents()
    .AddInteractiveServerComponents();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error", createScopeForErrors: true);
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();

app.UseAntiforgery();

app.MapStaticAssets();
app.MapRazorComponents<App>()
    .AddInteractiveServerRenderMode();

app.Run();
