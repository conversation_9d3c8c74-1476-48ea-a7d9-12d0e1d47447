You are tasked with converting a property listing record into a concise, descriptive sentence summarizing the key features of the residential realestate property listing in New York City. The listing data is provided in a structured format called ListingData. Your goal is to create a clear, informative summary that captures the essential details of the property.

**Schema Overview**  
Refer to the JSON schema provided, focusing on key fields and their descriptions (e.g., `UnitNumber` is ignored if `TOWNHOUSE`, `ListingDescription` removes HTML tags).

You are given a `ListingData` record containing information about a residential realestate property listing in New York City. 
The record is defined by following json schema:
```
{JSON_SCHEMA}
```

---

**Transformation Rules**  
Generate a **single sentence** summarizing the listing using this structure:  
**[ListingCategory] property at [Address] in [Location]. [Rooms/SQFT]. [Price] [Fees]. [Features]. [Descriptions].**

1. **Address**  
   - **Full Address**:  
     - `BuildingNumber` + `StreetName` + (`CrossStreets` in parentheses if present) + `BuildingName` (if exists).  
     - Example: `"123 Main St (between 1st Ave & 2nd Ave), The Towers"`.  
   - **Unit**: Add `"unit [UnitNumber]"` only if `UnitNumber` exists and is **not** `TOWNHOUSE`.  

2. **Location**  
   - Format as `"[Borough]'s [Neighborhood]"` (e.g., `Manhattan's Upper East Side`).  

3. **Rooms & SQFT**  
   - **Bedrooms/Bathrooms**: Use whole numbers if `.0` (e.g., `2` instead of `2.0`).  
   - **TotalRooms**: Include if provided and distinct from `Bedrooms + Bathrooms`.  
   - **SquareFootage**: Format as `X,XXX sqft` (e.g., `1,200 sqft`).  

4. **Price & Fees**  
   - **Price**:  
     - Sales: `$X,XXX,XXX`  
     - Rental: `$X,XXX/month`  
     - Append `($X per sqft, $Y per room)` if `PricePerSqFt`/`PricePerRoom` exist.  
   - **Fees**:  
     - `MaintCC`: `Maintenance fee: $X/month`  
     - `MonthlyExpence`: `Monthly expenses: $X`  

5. **Features**  
   - **Combine**:  
     - `ConstructionType` (e.g., `pre-war construction`).  
     - `BuildingPeriod` (e.g., `post-war`).  
     - `CommonOutdoorSpace` (e.g., `rooftop garden`).  
     - `BuildingFeatures` (split comma-separated values, lowercase, Oxford commas).  
     - `BuildingAllows`/`BuildingNotAllows` (e.g., `allows pets`, `no smoking`).  
   - Example: `"featuring pre-war construction, a rooftop garden, doorman, and gym"`.  

6. **Descriptions**  
   - Remove HTML tags from `ListingDescription`/`BuildingDescription`.  
   - Summarize key highlights (e.g., `"Boasting skyline views and renovated interiors"`).  

7. **Ownership & Listing Type**  
   - Include `OwnershipType` (e.g., `Condo`, `Co-op`) and `ListingType` (e.g., `exclusive listing`).  

---
### **Special Cases & Edge Handling**  
**1. UnitNumber Logic**  
- **Omit "unit"** if `UnitNumber` is `null`, empty, or matches `TOWNHOUSE` (case-insensitive).  
  - Example:  
    - *"123 Main St"* (no unit) vs. *"123 Main St, unit 5B"*.  

**2. Feature/Amenity Handling**  
- **Omit "featuring"** entirely if ALL of these are `null`/empty:  
  `ConstructionType`, `BuildingPeriod`, `CommonOutdoorSpace`, `BuildingFeatures`, `BuildingAllows`, `BuildingNotAllows`.  
- **Use Oxford commas** only if ≥2 features exist.  

**3. Fractional Numbers**  
- **Bedrooms/Bathrooms/TotalRooms**:  
  - Format `X.0` → `X` (whole number).  
  - Format `X.5` → `X.5` (never `½`).  
  - Example:  
    - `2.0` → `2 bedrooms`  
    - `1.5` → `1.5 bathrooms`  

**4. Empty Fields**  
- **Skip sections** if critical data is missing:  
  - No `Price` → Do not generate the sentence (incomplete data).  
  - No `Bathrooms`/`Bedrooms` → Use “studio” or omit room counts.  
  - No `SquareFootage` → Skip “sqft” references.  

**5. Ownership/Listing Type**  
- **Omit** `OwnershipType`/`ListingType` if generic (e.g., `Residential`, `Standard`).  
- **Include** if value adds context (e.g., `Condo`, `Co-op`, `Exclusive`).  

---
**Example Outputs**  
**Case 1: No Features, Fractional Bathroom**  
*"This rental property at 789 Broadway in Brooklyn's Williamsburg offers a studio with 1.5 bathrooms for $2,500/month. The building allows pets and includes a monthly expense of $200."*  

**Case 2: Townhouse, No Unit**  
*"Sales property at 10 Pine St (at Cedar St) in Manhattan's Financial District features 4 bedrooms, 3 bathrooms, and 3,000 sqft for $3,500,000 ($1,167 per sqft). The townhouse boasts pre-war construction and a private garden."*  

**Case 3: Empty Features**  
*"This co-op property at 200 Central Park West in Manhattan's Upper West Side offers 3 bedrooms and 2 bathrooms for $1,200,000. Monthly maintenance is $1,500."*  

---

Here is a `ListingData` record:
```json
{JSON_DATA}
```