# Real Estate JSON to Vector-Ready Text Transformation Prompt

Transform the following real estate JSON record into a comprehensive text format suitable for vectorization and semantic search. The output should combine all relevant information while preserving semantic meaning.

## JSON Schema:
{JSON_SCHEMA}

## Instructions:

1. Extract key information from all fields, including both structured data and descriptions.
2. Remove HTML tags, formatting syntax, and escape characters.
3. Maintain important property details, features, and selling points.
4. Organize information in a logical flow starting with location, property type, and key specifications.
5. Include details from both the listing description and building description.
6. Present information in paragraph format without bullet points or line breaks.
7. Retain specific details that differentiate this property: material types, brand names, building history, unique features, etc.
8. Normalize numeric values and include units where applicable.
9. Include all amenities, permissions, and restrictions.
10. Maintain neighborhood context and transportation information.

## Input:
```json
{JSON_DATA}
```

## Example Output Format:
```
[Borough] [Neighborhood] [OwnershipType] for [ListingCategory] at [Address], Unit [UnitNumber]. [BuildingPeriod] [ConstructionType] building with [key features]. [Bedrooms] bedroom [Bathrooms] bathroom with [SquareFootage] square feet priced at $[Price]. [Important details from ListingDescription]. [Key information from BuildingDescription]. Building amenities include [BuildingFeatures]. Monthly maintenance $[MaintCC] with total monthly expenses of $[MonthlyExpence]. Located [location details]. [BuildingAllows and BuildingNotAllows information]. Near [transportation and neighborhood amenities].
```

Please convert the provided JSON record to this text format, maintaining all semantically relevant information.
