<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <Platforms>AnyCPU;x64</Platforms>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="prompt.md" />
    <None Remove="prompt-ai.md" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="prompt.md">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="prompt-ai.md">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Dapper" Version="2.1.66" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="6.0.2" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.5" />
    <PackageReference Include="NJsonSchema" Version="11.3.2" />
    <PackageReference Include="Azure.AI.OpenAI" Version="2.1.0" />
	<PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.5" />
	<PackageReference Include="Microsoft.Extensions.AI" Version="9.5.0-preview.1.25262.9" />
	<PackageReference Include="Microsoft.Extensions.AI.OpenAI" Version="9.5.0-preview.1.25262.9" />
	<PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="9.0.5" />
    <PackageReference Include="Azure.Identity" Version="1.14.0" />
    <PackageReference Include="Qdrant.Client" Version="1.14.0" />
    <PackageReference Include="morelinq" Version="4.4.0" />
	  <PackageReference Include="Elastic.Clients.Elasticsearch" Version="8.18.0" />
  </ItemGroup>

  <ItemGroup>
    <None Update="appsettings.json">
	  <TransformOnBuild>true</TransformOnBuild>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
