﻿using Azure.AI.OpenAI;
using Azure.Identity;
using Elastic.Clients.Elasticsearch;
using Elastic.Clients.Elasticsearch.Mapping;
using Elastic.Transport;
using Microsoft.Extensions.AI;
using Microsoft.Extensions.Configuration;
using MoreLinq;
using NJsonSchema;
using Qdrant.Client;
using Qdrant.Client.Grpc;
using RealPlusNLP.DataLoader.Console;
using System.Text.Encodings.Web;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Text.Unicode;

const string collectionName = "listings-search";

var populateWithData = true;
var runSimilaritySearch = false;
var runSearch = false;

var configuration = new ConfigurationBuilder()
    .SetBasePath(Directory.GetCurrentDirectory())
    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
    .Build();

var dbService = new DatabaseService(configuration.GetConnectionString("RealPlusEntities")!);

var chatClient = new AzureOpenAIClient(
    new Uri("https://ai-foundry-dev.cognitiveservices.azure.com/"),
    new DefaultAzureCredential())
    .GetChatClient("gpt-4o")
    .AsIChatClient();
var embedding = new AzureOpenAIClient(
    new Uri("https://ai-foundry-dev.cognitiveservices.azure.com/"),
    new DefaultAzureCredential())
    .GetEmbeddingClient("text-embedding-ada-002")
    .AsIEmbeddingGenerator();
var chatOptions = new ChatOptions
{
    ResponseFormat = ChatResponseFormat.Text,
    Temperature = 0f
};

var settings = new ElasticsearchClientSettings(new Uri("https://rpls-dev-vector.es.eastus2.azure.elastic-cloud.com"))
    .Authentication(new BasicAuthentication("elastic", "Npgpn8W2uFC8N2myxbWSWacA"))
    .DefaultMappingFor<ListingDocument>(m => m
        .IndexName("listing-similarity")
        .IdProperty(p => p.Id));
var elasticClient = new ElasticsearchClient(settings);

var qdrantClient = new QdrantClient(
    host: "ca6ce12c-9371-4ac4-bac6-80ca53ec7e4d.europe-west3-0.gcp.cloud.qdrant.io",
    https: true,
    apiKey: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY2Nlc3MiOiJtIiwiZXhwIjoxNzQ2MzY3OTY5fQ.FI9ds-ChmoGd-ElScvjacZ_AWlkB5KncUmqUuUnTkGQ"
);
await InitializeCollectionAsync();

var options = new JsonSerializerOptions
{
    WriteIndented = false,
    DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
    Encoder = JavaScriptEncoder.Create(UnicodeRanges.All)
    //Encoder = JavaScriptEncoder.UnsafeRelaxedJsonEscaping
};
var schema = JsonSchema.FromType<ListingEntity>();
var jsonSchema = schema.ToJson(Newtonsoft.Json.Formatting.None);

var prompt = File.ReadAllText("prompt-ai.md")
    .Replace("{JSON_SCHEMA}", jsonSchema);

var ids = await dbService.GetListingIdsAsync();
var listings = await dbService.GetListingsAsync([.. ids]);

if (populateWithData)
{
    int count = 0;
    foreach (var listing in listings)
    {
        count++;
        try
        {
            var listingAsJson = JsonSerializer.Serialize(listing, options);
            var result = await chatClient
                .GetResponseAsync(prompt.Replace("{JSON_DATA}", listingAsJson), chatOptions);
            var listingAsText = result.Text;
            var vector = await embedding.GenerateEmbeddingVectorAsync(listingAsText);
            //await IndexListingAsync(listing, listingAsText, listingAsJson, vector.ToArray());
            await IndexListingInElasticAsync(listing, listingAsText, listingAsJson, vector.ToArray());
            Console.WriteLine($"Vectorized listing {listing.ListingNumber} ({listing.ListingId})");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Failed to vectorize listing {listing.ListingId}: {ex.Message}");
            throw;
        }
        // Console.WriteLine($"""
        //     *** JSON_DATA ***
        //     {listingAsJson}
        //     *** RESPONSE ***
        //     {listingAsText}

        //     """);
        if (count % 10 == 0)
        {
            Console.WriteLine($"Progress: {count}/{listings.Count()} listings processed");
        }
    }
}

if (runSimilaritySearch)
{
    // vector similarity test
    var l = listings.First();
    var lAsJson = JsonSerializer.Serialize(l, options);
    var lAsText = await chatClient.GetResponseAsync(prompt.Replace("{JSON_DATA}", lAsJson), chatOptions);
    var lVector = await embedding.GenerateEmbeddingVectorAsync(lAsText.Text);
    //var lr = await SearchListingsAsync(lVector.ToArray());
    var lr = await SearchListingsInElasticWithVectorAsync(lVector.ToArray());
    Console.WriteLine($"""
    ===========================================================================
    Vector Similarity for {l.ListingId}: {lAsText.Text}
    ***************************************************************************
    """);
    foreach (var r in lr.Where(r => r.Listing.Id != l.ListingId))
    {
        Console.WriteLine($"""
        Score:  {r.Score:P2}
        Id:     {r.Listing.Id}
        Text:   {r.Listing.AsText}
        ---
        """);
        // Console.WriteLine($"""
        // Score:  {r.Score:P2}
        // Id:     {r.Id.Num}
        // Text:   {r.Payload["text"].StringValue}
        // Json:   {r.Payload["json"].StringValue}
        // ---
        // """);
    }

    // ES similarity test
    // lr = await SearchListingsInElasticWithTextAsync(lAsText.Message.Text!);
    // Console.WriteLine($"""
    // ===========================================================================
    // ES Similarity for {l.ListingId}: {lAsText.Message.Text}
    // ***************************************************************************
    // """);
    // foreach (var r in lr.Take(3))
    // {
    //     Console.WriteLine($"""
    //     Score:  {r.Score:P2}
    //     Id:     {r.Listing.Id}
    //     Text:   {r.Listing.AsText}
    //     ---
    //     """);
    // }
}

if (runSearch)
{
    // vector search test
    var searchText = "not less then 3 bedrooms apartment in Upper West Side and not more then $3,000,000";
    var lsVector = await embedding.GenerateEmbeddingVectorAsync(searchText);
    var lsr = await SearchListingsInElasticWithVectorAsync(lsVector.ToArray());
    Console.WriteLine($"""
    ===========================================================================
    Vector Search for: {searchText}
    ***************************************************************************
    """);
    foreach (var r in lsr)
    {
        Console.WriteLine($"""
        Score:  {r.Score:P2}
        Id:     {r.Listing.Id}
        Text:   {r.Listing.AsText}
        ---
        """);
    }

    lsr = await SearchListingsInElasticWithTextAsync(searchText);
    Console.WriteLine($"""
    ===========================================================================
    ES Search for: {searchText}
    ***************************************************************************
    """);
    foreach (var r in lsr)
    {
        Console.WriteLine($"""
        Score:  {r.Score:P2}
        Id:     {r.Listing.Id}
        Text:   {r.Listing.AsText}
        ---
        """);
    }
}

Console.WriteLine("Press any key to exit...");
Console.ReadLine();

async Task InitializeCollectionAsync()
{
    // try
    // {
    //     if (!await qdrantClient.CollectionExistsAsync(collectionName))
    //     {
    //         var vectorsConfig = new VectorParams
    //         {
    //             Size = 1536,
    //             Distance = Distance.Cosine
    //         };

    //         await qdrantClient.CreateCollectionAsync(
    //             collectionName: collectionName,
    //             vectorsConfig: vectorsConfig
    //         );
    //     }
    // }
    // catch (Exception ex)
    // {
    //     throw new Exception($"Failed to initialize Qdrant collection: {ex.Message}", ex);
    // }
    var indexExists = await elasticClient.Indices.ExistsAsync<ListingDocument>();
    if (!indexExists.Exists)
    {
        var result = await elasticClient.Indices.CreateAsync<ListingDocument>(c => c
            .Mappings(m => m
                .Properties(p => p
                    .IntegerNumber(f => f.Id)
                    .Keyword(f => f.SecureGroup)
                    .Keyword(f => f.ListingCompany)
                    .Keyword(f => f.HasCopyForCompany!)
                    .Keyword(f => f.Status)
                    .Keyword(f => f.ListingNumber)
                    .Text(f => f.AsText)
                    .Text(f => f.AsJson, c => c.Index(false))
                    .DenseVector(f => f.TextVector, dv => dv
                        .Dims(1536)
                        .Index(true)
                        .Similarity(DenseVectorSimilarity.Cosine)
                    )
                )
            )
        // .Settings(s => s
        //     .NumberOfShards(1)
        // )
        );
        if (!result.IsValidResponse)
        {
            throw new Exception($"Failed to create Elasticsearch index: {result.ElasticsearchServerError?.Error?.Reason}");
        }
    }
}

async Task IndexListingAsync(
    ListingEntity listing,
    string listingAsText,
    string listingAsJson,
    float[] vector)
{
    var qdrantRecords = new PointStruct
    {
        Id = new PointId((ulong)listing.ListingId),
        Vectors = vector,
        Payload =
            {
                ["text"] = listingAsText,
                ["json"] = listingAsJson,
                ["listingNumber"] = listing.ListingNumber,
                // ["secureGroup"] = listing.SecureGroup,
                // ["listingCompany"] = listing.ListingCompany,
                // ["hasCopyForCompany"] = listing.HasCopyForCompany ?? string.Empty,
                // ["status"] = listing.Status
            }
    };

    try
    {
        await qdrantClient.UpsertAsync(collectionName, [qdrantRecords]);
    }
    catch (Exception ex)
    {
        throw new Exception($"Failed to save listing to Qdrant collection: {ex.Message}", ex);
    }
}
async Task IndexListingInElasticAsync(
    ListingEntity listing,
    string listingAsText,
    string listingAsJson,
    float[] vector)
{
    var response = await elasticClient.IndexAsync(
        new ListingDocument
        (
            listing.ListingId,
            listing.SecureGroup,
            listing.ListingCompany,
            listing.HasCopyForCompany,
            listing.Status,
            listing.ListingNumber,
            listingAsText,
            listingAsJson,
            vector
        )
    );
    if (!response.IsValidResponse)
    {
        throw new Exception($"Failed to save listing to Elasticsearch: {response.ElasticsearchServerError?.Error?.Reason}");
    }
}

// async Task IndexListingsAsync(List<ListingDataToVectorize> listingDataToVectorize)
// {
//     var qdrantRecords = listingDataToVectorize
//         .Select(ld => new PointStruct
//         {
//             Id = new PointId((ulong)ld.Listing.ListingId),
//             Vectors = ld.Vector,
//             Payload =
//                 {
//                     ["text"] = ld.ListingAsText,
//                     ["json"] = ld.ListingAsJson,
//                     ["listingNumber"] = ld.Listing.ListingNumber
//                 }
//         }).ToList();

//     try
//     {
//         await qdrantClient.UpsertAsync(collectionName, qdrantRecords);
//     }
//     catch (Exception ex)
//     {
//         throw new Exception($"Failed to save listings to Qdrant collection: {ex.Message}", ex);
//     }
// }

async Task<IReadOnlyList<ScoredPoint>> SearchListingsAsync(float[] vector)
{
    return await qdrantClient.SearchAsync(
        collectionName,
        vector,
        limit: 3
    );
}
async Task<IReadOnlyList<ListingSearchResult>> SearchListingsInElasticWithVectorAsync(float[] vector)
{
    var response = await elasticClient
        .SearchAsync<ListingDocument>(s => s
            .Knn(k => k
                .Field(f => f.TextVector)
                .QueryVector(vector)
                .k(5)
                .NumCandidates(5 * 15)
            )
            .Size(5));

    var results = new List<ListingSearchResult>();
    foreach (var hit in response.Hits)
    {
        var src = hit.Source!;
        var score = hit.Score;
        results.Add(new ListingSearchResult(
            src,
            score
        ));
    }

    return results;
}
async Task<IReadOnlyList<ListingSearchResult>> SearchListingsInElasticWithTextAsync(string searchText)
{
    var response = await elasticClient
            .SearchAsync<ListingDocument>(s => s
                .Query(q => q
                    .Match(m => m
                        .Field(f => f.AsText)
                        .Query(searchText)
                    )
                )
                .Size(5));

    double maxScore = (float)response.Hits.Max(h => h.Score ?? 0);
    var results = new List<ListingSearchResult>();
    foreach (var hit in response.Hits)
    {
        var src = hit.Source!;
        var score = hit.Score / maxScore;
        results.Add(new ListingSearchResult(
            src,
            score
        ));
    }

    return results;
}

sealed record ListingDataToVectorize(
    ListingEntity Listing,
    string ListingAsText,
    string ListingAsJson,
    float[] Vector);

sealed record ListingSearchResult(
    ListingDocument Listing,
    double? Score);