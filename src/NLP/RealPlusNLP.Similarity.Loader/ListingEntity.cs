﻿using System.ComponentModel;
using System.Text.Json.Serialization;

namespace RealPlusNLP.DataLoader.Console;

public sealed class ListingEntity
{
    [JsonIgnore]
    public int ListingId { get; set; }
    [JsonIgnore]
    public string ListingNumber { get; set; } = default!;
    [JsonIgnore]
    public string SecureGroup { get; set; } = default!;
    [JsonIgnore]
    public string ListingCompany { get; set; } = default!;
    [JsonIgnore]
    public string? HasCopyForCompany { get; set; }
    [Description("Sales or Rental")]
    public string ListingCategory { get; set; } = default!;
    public string ListingType { get; set; } = default!;
    public string OwnershipType { get; set; } = default!;
    [JsonIgnore]
    public string Status { get; set; } = default!;
    public string Borough { get; set; } = default!;
    public string Neighborhood { get; set; } = default!;
    public string Address { get; set; } = default!;
    public string StreetName { get; set; } = default!;
    public string? CrossStreets { get; set; }
    public string? BuildingName { get; set; }
    public string BuildingNumber { get; set; } = default!;
    [Description("Ignore when the value is TOWNHOUSE")]
    public string? UnitNumber { get; set; }
    public float? Bathrooms { get; set; }
    public float? Bedrooms { get; set; }
    public float? TotalRooms { get; set; }
    public int? SquareFootage { get; set; }
    public int Price { get; set; }
    public double? PricePerSqFt { get; set; }
    public int? PricePerRoom { get; set; }
    [Description("Maintenance or Common Charges")]
    public int? MaintCC { get; set; }
    public int? MonthlyExpence { get; set; }
    public double? Longitude { get; set; }
    public double? Latitude { get; set; }
    public string? ConstructionType { get; set; }
    public string? CommonOutdoorSpace { get; set; }
    public string? BuildingFeatures { get; set; }
    [Description("Post-war or Pre-war")]
    public string? BuildingPeriod { get; set; }
    public string? BuildingAllows { get; set; }
    public string? BuildingNotAllows { get; set; }
    [Description("Remove all HTML tags")]
    public string? ListingDescription { get; set; }
    [Description("Remove all HTML tags")]
    public string? BuildingDescription { get; set; }
}
