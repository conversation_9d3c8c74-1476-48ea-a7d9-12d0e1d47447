﻿using Dapper;
using Microsoft.Data.SqlClient;
using System.Data;

namespace RealPlusNLP.DataLoader.Console;

public sealed class DatabaseService(string connectionString)
{
    private readonly string _connectionString = connectionString;

    public IDbConnection CreateConnection() => new SqlConnection(_connectionString);

    public async Task<IEnumerable<int>> GetListingIdsAsync()
    {
        using var connection = CreateConnection();

        return await connection.QueryAsync<int>("""
            SELECT TOP 500 listingId
              FROM listings
             WHERE securegroup='CORC'
               AND saleorrental = 8
               AND status IN (163,3593,3594,3688,3595,3687,3596,3689)
            ORDER BY listingId DESC
            """
            );
    }

    public async Task<IEnumerable<ListingEntity>> GetListingsAsync(int[] listingIds)
    {
        using var connection = CreateConnection();

        var listingIdTable = new DataTable();
        listingIdTable.Columns.Add("ID", typeof(int));
        foreach (var id in listingIds)
        {
            listingIdTable.Rows.Add(id);
        }

        var parameter = new DynamicParameters();
        parameter.Add("@ListingsIDs", listingIdTable.AsTableValuedParameter("dbo.IDList"));

        return await connection.QueryAsync<ListingEntity>(
            "GetListingsForVectorization",
            parameter,
            commandType: CommandType.StoredProcedure
        );
    }
}
