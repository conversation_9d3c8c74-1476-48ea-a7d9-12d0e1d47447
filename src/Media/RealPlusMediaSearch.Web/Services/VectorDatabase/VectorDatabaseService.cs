﻿using Qdrant.Client;
using Qdrant.Client.Grpc;

namespace RealPlusMediaSearch.Web.Services.VectorDatabase;

public class VectorDatabaseService() : IVectorDatabaseService
{
    readonly QdrantClient qClient = new(
        host: "ca6ce12c-9371-4ac4-bac6-80ca53ec7e4d.europe-west3-0.gcp.cloud.qdrant.io",
        https: true,
        apiKey: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY2Nlc3MiOiJtIiwiZXhwIjoxNzQ2MzY3OTY5fQ.FI9ds-ChmoGd-ElScvjacZ_AWlkB5KncUmqUuUnTkGQ"
    );

    public async Task SaveImagestoDb(List<VectorizedImage> images)
    {
        var qdrantRecords = new List<PointStruct>();

        foreach (var image in images)
        {
            qdrantRecords.Add(new PointStruct()
            {
                Id = new PointId((uint)new Random().Next(0, 10000000)),
                Vectors = image.Vectors,
                Payload =
                {
                    ["name"] = image.FileName
                }
            });
        }

        await qClient.UpsertAsync("images", qdrantRecords);
    }

    public async Task<List<VectorizedImage>> GetAllImages()
    {
        var results = await qClient.QueryAsync(
            collectionName: "images",
            limit: 100
        );

        List<VectorizedImage> images = [];

        //var fileNames = Directory.GetFiles(env.WebRootPath + "\\images").ToList();

        foreach (var image in results)
        {
            images.Add(new VectorizedImage() { ImagePath = $"/images/{image.Payload["name"].StringValue}" });
        }

        return images;
    }

    public async Task<IReadOnlyList<ScoredPoint>> SearchImages(float[] vector, ulong limit)
    {
        return await qClient.QueryAsync(
            collectionName: "images",
            query: vector,
            limit: limit
        );
    }

    public async Task TryCreateDb()
    {
        try
        {
            await qClient.CreateCollectionAsync("images", new VectorParams { Size = 1024, Distance = Distance.Cosine });
        }
        catch
        {
            // already exists
        }
    }
}
