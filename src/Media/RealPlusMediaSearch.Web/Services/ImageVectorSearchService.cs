﻿using Azure.AI.Vision.ImageAnalysis;
using Elastic.Clients.Elasticsearch;
using Elastic.Clients.Elasticsearch.Mapping;
using Elastic.Clients.Elasticsearch.QueryDsl;
using Microsoft.Extensions.AI;
using Qdrant.Client;
using Qdrant.Client.Grpc;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace RealPlusMediaSearch.Web.Services;

public sealed record ImageSearchItem(
    string FileName,
    ImageData ImageData,
    double? Score = null)
{
    public string FilePath => $"/images/{FileName}";
}
public sealed record ImageData(ImageAnalysis ImageAnalysis, float[]? Vector = null);
public sealed record ImageAnalysis(
    string Caption,
    string[] DenseCaptions,
    string[] Objects,
    string[] Tags,
    string[] Reads);

public sealed record VectorizationResponse(
    [property: JsonPropertyName("vector")] float[] Vector,
    [property: JsonPropertyName("modelVersion")] string ModelVersion);

public class ImageVectorSearchService(
    ImageAnalysisClient imageAnalysisClient,
    QdrantClient qdrantClient,
    HttpClient httpClient,
    ElasticsearchClient elasticClient,
    IEmbeddingGenerator<string, Embedding<float>> embeddingGenerator) : IDisposable
{
    private const string collectionName = "images-search";
    private const ulong vectorSize = 1024;

    private const string vecVersion = "?api-version=2024-02-01&model-version=2023-04-15";
    private const string vecImgUrl = "computervision/retrieval:vectorizeImage" + vecVersion;
    private const string vecTxtUrl = "computervision/retrieval:vectorizeText" + vecVersion;

    public async Task InitializeCollectionAsync()
    {
        // try
        // {
        //     if (!await qdrantClient.CollectionExistsAsync(collectionName))
        //     {
        //         var vectorsConfig = new VectorParams
        //         {
        //             Size = vectorSize,
        //             Distance = Qdrant.Client.Grpc.Distance.Cosine
        //         };
        //         await qdrantClient.CreateCollectionAsync(
        //             collectionName: collectionName,
        //             vectorsConfig: vectorsConfig
        //         );
        //     }
        // }
        // catch (Exception ex)
        // {
        //     throw new Exception($"Failed to initialize Qdrant collection: {ex.Message}", ex);
        // }

        var indexExists = await elasticClient.Indices.ExistsAsync<ListingMediaDocument>();
        if (!indexExists.Exists)
        {
            var result = await elasticClient.Indices.CreateAsync<ListingMediaDocument>(c => c
                .Mappings(m => m
                    .Properties(p => p
                        .Keyword(f => f.FileName)
                        .Text(f => f.Caption)
                        .Text(f => f.DenseCaptions)
                        .Text(f => f.Objects)
                        .Text(f => f.Tags)
                        .Text(f => f.Reads)
                        .DenseVector(f => f.ImageVector, dv => dv
                            .Dims(1024)
                            .Index(true)
                            .Similarity(DenseVectorSimilarity.Cosine)
                        )
                        .DenseVector(f => f.CaptionVector, dv => dv
                            .Dims(1536)
                            .Index(true)
                            .Similarity(DenseVectorSimilarity.Cosine)
                        )
                    )
                )
            // .Settings(s => s
            //     .NumberOfShards(1)
            // )
            );
            if (!result.IsValidResponse)
            {
                throw new Exception($"Failed to create Elasticsearch index: {result.ElasticsearchServerError?.Error?.Reason}");
            }
        }
    }

    public async Task SaveImagesAsync(List<ImageSearchItem> images)
    {
        var qdrantRecords = images.Select(image => new PointStruct
        {
            Id = new PointId((uint)new Random().Next(0, 10000000)),
            Vectors = image.ImageData.Vector!,
            Payload =
            {
                ["name"] = image.FileName,
                ["caption"] = image.ImageData.ImageAnalysis.Caption,
                ["denseCaptions"] = JsonSerializer.Serialize(image.ImageData.ImageAnalysis.DenseCaptions),
                ["objects"] = JsonSerializer.Serialize(image.ImageData.ImageAnalysis.Objects),
                ["tags"] = JsonSerializer.Serialize(image.ImageData.ImageAnalysis.Tags),
                ["reads"] = JsonSerializer.Serialize(image.ImageData.ImageAnalysis.Reads)
            }
        }).ToList();

        try
        {
            await qdrantClient.UpsertAsync(collectionName, qdrantRecords);
        }
        catch (Exception ex)
        {
            throw new Exception($"Failed to save images to Qdrant collection: {ex.Message}", ex);
        }
    }

    public async Task SaveImagesInElasticSearchAsync(List<ImageSearchItem> images)
    {
        var elasticRecords = await Task.WhenAll(images.Select(async image => new ListingMediaDocument(
            image.FileName,
            image.ImageData.ImageAnalysis.Caption,
            image.ImageData.ImageAnalysis.DenseCaptions,
            image.ImageData.ImageAnalysis.Objects,
            image.ImageData.ImageAnalysis.Tags,
            image.ImageData.ImageAnalysis.Reads,
            image.ImageData.Vector!,
            await EmbedTextAsync(image.ImageData.ImageAnalysis.Caption)
        )).ToList());

        var response = await elasticClient.IndexManyAsync(elasticRecords);
        if (!response.IsValidResponse)
        {
            throw new Exception($"Failed to save images to Elasticsearch: {response.ElasticsearchServerError?.Error?.Reason}");
        }
    }

    public async Task<List<ImageSearchItem>> GetAllImagesAsync()
    {
        var results = await qdrantClient.QueryAsync(
            collectionName: collectionName,
            vectorsSelector: true,
            limit: 100
        );

        return [.. results.Select(img => new ImageSearchItem(
            img.Payload["name"].StringValue,
            new ImageData(
                new ImageAnalysis(
                    img.Payload["caption"].StringValue ?? string.Empty,
                    JsonSerializer.Deserialize<string[]>(img.Payload["denseCaptions"].StringValue) ?? Array.Empty<string>(),
                    JsonSerializer.Deserialize<string[]>(img.Payload["objects"].StringValue) ?? Array.Empty<string>(),
                    JsonSerializer.Deserialize<string[]>(img.Payload["tags"].StringValue) ?? Array.Empty<string>(),
                    JsonSerializer.Deserialize<string[]>(img.Payload["reads"].StringValue) ?? Array.Empty<string>()
                )
            ),
            img.Score
        ))];
    }

    public async Task<List<ImageSearchItem>> GetAllImagesFromElasticAsync()
    {
        var response = await elasticClient
            .SearchAsync<ListingMediaDocument>(s => s
                .Index("listing-media")
                .Size(100));

        return [.. response.Documents.Select(image => new ImageSearchItem(
            image.FileName,
            new ImageData(
                new ImageAnalysis(
                    image.Caption,
                    image.DenseCaptions,
                    image.Objects,
                    image.Tags,
                    image.Reads
                )
            ),
            0
        ))];
    }

    public async Task<List<ImageSearchItem>> SearchImagesAsync(float[] vector, ulong limit = 10)
    {
        var results = await qdrantClient.QueryAsync(
            collectionName: collectionName,
            query: vector,
            vectorsSelector: true,
            limit: limit
        );

        return [.. results.Select(img => new ImageSearchItem(
            img.Payload["name"].StringValue,
            new ImageData(
                new ImageAnalysis(
                    img.Payload["caption"].StringValue ?? string.Empty,
                    JsonSerializer.Deserialize<string[]>(img.Payload["denseCaptions"].StringValue) ?? [],
                    JsonSerializer.Deserialize<string[]>(img.Payload["objects"].StringValue) ?? [],
                    JsonSerializer.Deserialize<string[]>(img.Payload["tags"].StringValue) ?? [],
                    JsonSerializer.Deserialize<string[]>(img.Payload["reads"].StringValue) ?? []
                )
            ),
            img.Score
        ))];
    }

    public async Task<List<ImageSearchItem>> SearchImagesAsync(string searchString, ulong limit = 10)
    {
        float[]? vector = searchString.StartsWith("http://") || searchString.StartsWith("https://") ?
            await VectorizeImageAsync(searchString) :
            await VectorizeTextAsync(searchString);

        var results = await qdrantClient.QueryAsync(
            collectionName: collectionName,
            query: vector,
            vectorsSelector: true,
            limit: limit
        );

        return [.. results.Select(img => new ImageSearchItem(
            img.Payload["name"].StringValue,
            new ImageData(
                new ImageAnalysis(
                    img.Payload["caption"].StringValue ?? string.Empty,
                    JsonSerializer.Deserialize<string[]>(img.Payload["denseCaptions"].StringValue) ?? [],
                    JsonSerializer.Deserialize<string[]>(img.Payload["objects"].StringValue) ?? [],
                    JsonSerializer.Deserialize<string[]>(img.Payload["tags"].StringValue) ?? [],
                    JsonSerializer.Deserialize<string[]>(img.Payload["reads"].StringValue) ?? []
                )
            ),
            img.Score
        ))];
    }

    public async Task<List<ImageSearchItem>> SearchImagesInElasticAsync(
        string searchString, string textSearchType, int limit = 10)
    {
        var isImageSearch = searchString.StartsWith("http://") || searchString.StartsWith("https://");
        float[]? vector = isImageSearch ?
            await VectorizeImageAsync(searchString) :
            textSearchType == "vector" ? await EmbedTextAsync(searchString) : null;
        SearchResponse<ListingMediaDocument>? response;

        if (isImageSearch)
        {
            response = await elasticClient
                .SearchAsync<ListingMediaDocument>(s => s
                    .Index("listing-media")
                    .Knn(k => k
                        .Field(f => f.ImageVector)
                        .QueryVector(vector)
                        .k(limit)
                        .NumCandidates(limit * 10)
                    )
                    .Size(limit));
            // response = await elasticClient
            //     .SearchAsync<ListingMediaDocument>(s => s
            //         .Query(q => q
            //             .ScriptScore(ss => ss
            //                 .Query(new MatchAllQuery())
            //                 .Script(script => script
            //                     .Source("(cosineSimilarity(params.query_vector, 'imageVector') + 1.0) / 2.0")
            //                     .Params(p => p
            //                         .Add("query_vector", vector!)
            //                     )
            //                 )
            //             )
            //         )
            //         .Size(limit));
        }
        else if (textSearchType == "elastic")
        {
            var caption = Infer.Field<ListingMediaDocument>(f => f.Caption);

            response = await elasticClient
                .SearchAsync<ListingMediaDocument>(s => s
                    .Index("listing-media")
                    .Query(q => q
                        .MultiMatch(m => m
                            .Query(searchString)
                            .Type(TextQueryType.BestFields)
                            .Fields(new[]
                            {
                                Infer.Field<ListingMediaDocument>(f => f.Caption),
                                Infer.Field<ListingMediaDocument>(f => f.DenseCaptions),
                                Infer.Field<ListingMediaDocument>(f => f.Objects),
                                Infer.Field<ListingMediaDocument>(f => f.Tags),
                                Infer.Field<ListingMediaDocument>(f => f.Reads)
                            })
                        )
                    )
                    .Size(limit));
        }
        else
        {
            response = await elasticClient
                .SearchAsync<ListingMediaDocument>(s => s
                    .Index("listing-media")
                    .Knn(k => k
                        .Field(f => f.CaptionVector)
                        .QueryVector(vector)
                        .k(limit)
                        .NumCandidates(limit * 10)
                    )
                    .Size(limit));
        }

        double maxScore = 0;
        if (!isImageSearch && textSearchType == "elastic" && response.Hits.Count != 0)
        {
            maxScore = (float)response.Hits.Max(h => h.Score ?? 0);
        }

        // Create a list of search results with their corresponding scores
        var results = new List<ImageSearchItem>();
        foreach (var hit in response.Hits)
        {
            var img = hit.Source!;
            var score = hit.Score;
            if (!isImageSearch && textSearchType == "elastic")
            {
                score /= maxScore;
            }
            results.Add(new ImageSearchItem(
                img.FileName,
                new ImageData(
                    new ImageAnalysis(
                        img.Caption,
                        img.DenseCaptions,
                        img.Objects,
                        img.Tags,
                        img.Reads
                    )
                ),
                score
            ));
        }

        return results;
    }

    public async Task<ImageData> VectorizeAndAnalyzeImageAsync(byte[] imageBytes)
    {
        var vector = await VectorizeImageAsync(imageBytes);
        var analysis = await AnalyzeImageAsync(imageBytes);

        return new ImageData(analysis, vector);
    }

    public async Task<ImageAnalysis> AnalyzeImageAsync(byte[] imageBytes)
    {
        try
        {
            var result = await imageAnalysisClient.AnalyzeAsync(
                BinaryData.FromBytes(imageBytes),
                VisualFeatures.Caption |
                VisualFeatures.DenseCaptions |
                VisualFeatures.Objects |
                VisualFeatures.Read |
                VisualFeatures.Tags);
            var val = result.Value;

            return new ImageAnalysis(
                $"{val.Caption.Text} ({val.Caption.Confidence:P2})",
                val.DenseCaptions.Values.Select(c => $"{c.Text} ({c.Confidence:P2})").ToArray(),
                val.Objects.Values.SelectMany(o => o.Tags).Select(tag => $"{tag.Name} ({tag.Confidence:P2})").ToArray(),
                val.Tags.Values.Select(tag => $"{tag.Name} ({tag.Confidence:P2})").ToArray(),
                val.Read.Blocks.SelectMany(block => block.Lines).Select(line => line.Text).ToArray()
                );
        }
        catch (Exception ex)
        {
            throw new Exception($"Failed to analyze local image: {ex.Message}", ex);
        }
    }

    public async Task<float[]> VectorizeImageAsync(byte[] imageBytes)
    {
        HttpRequestMessage request = new(HttpMethod.Post, vecImgUrl);
        var binaryContent = new ByteArrayContent(imageBytes);
        binaryContent.Headers.ContentType = new MediaTypeHeaderValue("application/octet-stream");
        request.Content = binaryContent;

        var response = await httpClient.SendAsync(request);

        var responseJson = await response.Content.ReadAsStringAsync();
        var responseObj = JsonSerializer.Deserialize<VectorizationResponse>(responseJson);

        return responseObj!.Vector;
    }

    public async Task<float[]> VectorizeImageAsync(string imageUrl)
    {
        HttpRequestMessage request = new(HttpMethod.Post, vecImgUrl)
        {
            Content = new StringContent(JsonSerializer.Serialize(new { url = imageUrl }),
                Encoding.UTF8, "application/json")
        };

        var response = await httpClient.SendAsync(request);

        var responseJson = await response.Content.ReadAsStringAsync();
        var responseObj = JsonSerializer.Deserialize<VectorizationResponse>(responseJson);

        return responseObj!.Vector;
    }

    public async Task<float[]> VectorizeTextAsync(string prompt)
    {
        HttpRequestMessage request = new(HttpMethod.Post, vecTxtUrl)
        {
            Content = new StringContent(
                JsonSerializer.Serialize(new { text = prompt }),
                Encoding.UTF8,
                "application/json")
        };

        var response = await httpClient.SendAsync(request);

        var responseJson = await response.Content.ReadAsStringAsync();
        var responseObj = JsonSerializer.Deserialize<VectorizationResponse>(responseJson);

        return responseObj!.Vector;
    }

    public async Task<float[]> EmbedTextAsync(string prompt)
    {
        var vector = await embeddingGenerator.GenerateEmbeddingVectorAsync(prompt);

        return vector.ToArray();
    }

    public void Dispose()
    {
        qdrantClient?.Dispose();
        GC.SuppressFinalize(this);
    }
}
