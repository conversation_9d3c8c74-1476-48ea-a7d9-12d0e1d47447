@page "/"

@using System.ComponentModel.DataAnnotations
@using System.Net
@using System.Text
@using System.Text.Json.Serialization
@using System.Text.Json
@using RealPlusMediaSearch.Web.Services
@using System.Net.Http.Headers

@inject ImageVectorSearchService imageVectorSearchService
@inject IWebHostEnvironment env
@inject ISnackbar Snackbar

<PageTitle>Media Search</PageTitle>

<MudDrawer Open="@isDrawerOpen" Anchor="Anchor.End" Elevation="1" Variant="@DrawerVariant.Temporary" Width="400px" OpenChanged="OnDrawerOpenChanged">
    @if (selectedImage != null)
    {
        <MudContainer Class="pa-6">
            <MudText Typo="Typo.h6" Class="mb-4">Image Details</MudText>

            <MudPaper Class="pa-4 mb-4" Elevation="0">
                <MudText Typo="Typo.subtitle1" Class="mb-2"><strong>Score</strong></MudText>
                <MudText>@selectedImage.Score?.ToString("P2")</MudText>
            </MudPaper>

            <MudPaper Class="pa-4 mb-4" Elevation="0">
                <MudText Typo="Typo.subtitle1" Class="mb-2"><strong>Caption</strong></MudText>
                <MudText>@selectedImage.ImageData.ImageAnalysis.Caption</MudText>
            </MudPaper>

            <MudPaper Class="pa-4 mb-4" Elevation="0">
                <MudText Typo="Typo.subtitle1" Class="mb-2"><strong>Dense Captions</strong></MudText>
                <MudChipSet T="string">
                    @foreach (var caption in selectedImage.ImageData.ImageAnalysis.DenseCaptions)
                    {
                        <MudChip Size="Size.Small" Color="Color.Default">@caption</MudChip>
                    }
                </MudChipSet>
            </MudPaper>

            <MudPaper Class="pa-4 mb-4" Elevation="0">
                <MudText Typo="Typo.subtitle1" Class="mb-2"><strong>Objects</strong></MudText>
                <MudChipSet T="string">
                    @foreach (var obj in selectedImage.ImageData.ImageAnalysis.Objects)
                    {
                        <MudChip Size="Size.Small" Color="Color.Default">@obj</MudChip>
                    }
                </MudChipSet>
            </MudPaper>

            <MudPaper Class="pa-4 mb-4" Elevation="0">
                <MudText Typo="Typo.subtitle1" Class="mb-2"><strong>Tags</strong></MudText>
                <MudChipSet T="string">
                    @foreach (var tag in selectedImage.ImageData.ImageAnalysis.Tags)
                    {
                        <MudChip Size="Size.Small" Color="Color.Default">@tag</MudChip>
                    }
                </MudChipSet>
            </MudPaper>

            @if (selectedImage.ImageData.ImageAnalysis.Reads.Any())
            {
                <MudPaper Class="pa-4 mb-4" Elevation="0">
                    <MudText Typo="Typo.subtitle1" Class="mb-2"><strong>Text in Image</strong></MudText>
                    <MudChipSet T="string">
                        @foreach (var text in selectedImage.ImageData.ImageAnalysis.Reads)
                        {
                            <MudChip Size="Size.Small" Color="Color.Default">@text</MudChip>
                        }
                    </MudChipSet>
                </MudPaper>
            }
        </MudContainer>
    }
</MudDrawer>

<MudGrid class="mb-12">
    @* Search form *@
    <MudItem xs="8">
        <MudCard>
            <MudCardContent>
                <MudText><p>Enter a prompt or paste an image url to search your images. The search is AI powered, so just enter whatever natural language text describes what you're looking for</p></MudText>
            </MudCardContent>
        <EditForm Model="@model" OnValidSubmit="SearchImages">
            <DataAnnotationsValidator />
            <MudCard>
                <MudCardContent>
                    <MudGrid>
                        <MudItem xs="6">
                            <MudTextField Label="Type a prompt or paste an image url" @bind-Value="model.SearchPrompt" For="@(() => model.SearchPrompt)"/>
                        </MudItem>
                        <MudItem xs="3">
                            <MudSelect T="int" @bind-Value="model.ResultLimit" Label="Result limit" Placeholder="Result limit">
                                @for (int i = 5; i <= 20; i+=5)
                                {
                                    var z = i; // Need to separate iterator from displayed value
                                    <MudSelectItem T="int" Value="@i">@z</MudSelectItem>

                                }
                            </MudSelect>
                        </MudItem>
                        <MudItem xs="3">
                            @* <MudSelect T="double" @bind-Value="model.ConfidenceThreshold" Label="Text Search Type" Placeholder="Text Search Type"> *@
                            <MudSelect T="string" @bind-Value="model.TextSearchType" Label="Search with" Placeholder="Text Search Type">
                                @foreach(var textSearchType in textSearchTypes)
                                {
                                    <MudSelectItem T="string" Value="@textSearchType.Value">@textSearchType.Key</MudSelectItem>

                                }
                            </MudSelect>
                            @* <MudSelect T="double" @bind-Value="model.ConfidenceThreshold" Label="Confidence Threshold" Placeholder="Confidence Threshold">
                                @foreach(var threshold in confidenceThresholds)
                                {
                                    <MudSelectItem T="double" Value="@threshold.Value">@threshold.Key</MudSelectItem>

                                }
                            </MudSelect> *@
                        </MudItem>
                    </MudGrid>
                    <br />
                    <div style="float: right">
                        <MudButton ButtonType="ButtonType.Button" OnClick="ResetUI" Variant="Variant.Filled" Color="Color.Primary" Class="ml-auto mr-5">Reset</MudButton>
                        <MudButton ButtonType="ButtonType.Submit" Variant="Variant.Filled" Color="Color.Primary" Class="ml-auto">Search</MudButton>
                    </div>
                </MudCardContent>
            </MudCard>
        </EditForm>
        </MudCard>
    </MudItem>
    @* Upload form *@
    <MudItem xs="4">
        <MudStack Style="width: 100%">
            <MudFileUpload T="IReadOnlyList<IBrowserFile>"
            @ref="@_fileUpload"
            OnFilesChanged="OnInputFileChanged"
            AppendMultipleFiles
            Hidden="@false"
            InputClass="absolute mud-width-full mud-height-full overflow-hidden z-10"
            InputStyle="opacity:0"
            tabindex="-1"
            @ondrop="@ClearDragClass"
            @ondragenter="@SetDragClass"
            @ondragleave="@ClearDragClass"
            @ondragend="@ClearDragClass">
                <ActivatorContent>
                    <MudPaper Height="150px" Outlined="true" Class="@_dragClass">
                        <MudText Typo="Typo.h6">Drag and drop files here or click to upload your media</MudText>
                        @foreach (var file in _fileNames)
                        {
                            <MudChip T="string" Color="Color.Dark" Text="@file" tabindex="-1" />
                        }
                    </MudPaper>
                </ActivatorContent>
            </MudFileUpload>
            @if(!isUploading)
            {
                <MudToolBar Gutters="@false"
                Class="relative d-flex justify-end gap-4">
                    <MudButton Color="Color.Primary" OnClick="@OpenFilePickerAsync" Variant="Variant.Filled">Open file picker</MudButton>
                    <MudButton Color="Color.Primary" Disabled="@(!_fileNames.Any())" OnClick="@Upload" Variant="Variant.Filled">Upload</MudButton>
                    <MudButton Color="Color.Error" Disabled="@(!_fileNames.Any())" OnClick="@ClearAsync" Variant="Variant.Filled">Clear</MudButton>
                </MudToolBar>
            } else
            {
                <MudText>Uploading file(s)...</MudText><MudProgressCircular Color="Color.Default" Indeterminate="true" />
            }
        </MudStack>
    </MudItem>
</MudGrid>

@* Search results *@
<MudGrid>
    @if(!isSearching)
    {
        @if(!string.IsNullOrEmpty(model.SearchPrompt) && images.Count == 0){
            <MudItem xs="12">
                <MudText Typo="Typo.h3">No relevant results.</MudText>
            </MudItem>
        }
        else
        {
            @foreach (var image in images)
            {
                <MudItem xs="3" Style="overflow: hidden"> @*max-height: 200px;*@
                    <div style="position: relative;">
                        <MudImage ObjectFit="ObjectFit.Cover"
                                 Fluid="true"
                                 Src="@image.FilePath"
                                 Elevation="25"
                                 Class="@($"rounded-lg {(selectedImage == image ? "selected-image" : "")}")"
                                 Style="@(selectedImage == image ? "border: 3px solid var(--mud-palette-primary); transition: border 0.2s ease;" : "")"
                                 @onclick="() => OnImageClick(image)"/>
                        @if (image.Score != null && image.Score > 0)
                        {
                            <div style="position: absolute; bottom: 8px; right: 8px; background-color: rgba(0,0,0,0.6); color: white; padding: 4px 8px; border-radius: 4px; font-size: 0.875rem;">
                                @(image.Score?.ToString("P2"))
                            </div>
                        }
                    </div>
                </MudItem>
            }
        }
    }
    else
    {
        <MudItem xs="12">
            <MudText>Working on that super quick...</MudText><MudProgressCircular Color="Color.Default" Indeterminate="true" />
        </MudItem>
    }
</MudGrid>

@code
{
    //HttpClient client { get; set; }

    // Search model and data
    SearchForm model = new SearchForm()
    {
        ResultLimit = 10,
        ConfidenceThreshold = 0.355,
        TextSearchType = "vector"
    };
    private List<KeyValuePair<string, double>> confidenceThresholds = new()
    {
        new("High", 0.355),
        new("Medium", 0.335),
        new("Low", 0.30)
    };
    private List<KeyValuePair<string, string>> textSearchTypes = new()
    {
        new("Text", "elastic"),
        new("Vector", "vector")
    };
    private List<ImageSearchItem> images = new();
    private ImageSearchItem? selectedImage;
    private bool isDrawerOpen;

    // Image upload data
    private readonly List<string> _fileNames = new();
    private MudFileUpload<IReadOnlyList<IBrowserFile>>? _fileUpload;
    IReadOnlyList<IBrowserFile> ImageUploads;

    // Miscellaneous UI data
    bool isUploading = false;
    bool isSearching = false;
    private long maxFileSize = 10240000;
    private const string DefaultDragClass = "relative rounded-lg border-2 border-dashed pa-4 mud-width-full mud-height-full";
    private string _dragClass = DefaultDragClass;

    protected override async Task OnInitializedAsync()
    {
        // Initial setup - make sure db exists, get httpclient instance, get images that were previously uploaded
        //await vectorDbService.TryCreateDb();
        await imageVectorSearchService.InitializeCollectionAsync();
        //client = httpClientFactory.CreateClient("vision");
        images = await imageVectorSearchService.GetAllImagesFromElasticAsync();

        await base.OnInitializedAsync();
    }

    private async Task Upload()
    {
        // Clear UI lists and status
        isUploading = true;
        try
        {
            var vectorizedImages = new List<ImageSearchItem>();

            foreach(var file in ImageUploads)
            {
                // Construct folder path and save uploaded image there so it can be retrieved from the UI
                string safeFileName = WebUtility.HtmlEncode(file.Name);
                var path = Path.Combine(env.WebRootPath, "images", safeFileName);
                await using FileStream fs = new(path, FileMode.Create);
                await file.OpenReadStream(maxFileSize).CopyToAsync(fs);

                // Get a vector for each image from Azure Vision
                await using MemoryStream ms = new();
                await file.OpenReadStream(maxFileSize).CopyToAsync(ms);
                var vectorResponse = await imageVectorSearchService.VectorizeAndAnalyzeImageAsync(ms.ToArray());
                vectorizedImages.Add(new ImageSearchItem(file.Name, vectorResponse));
                // var vectorResponse = await visionService.VectorizeImage(ms.ToArray());
                // vectorizedImages.Add(new VectorizedImage() { Vectors = vectorResponse.vector, FileName = file.Name });
            }

            // Save image vectors to db
            //await vectorDbService.SaveImagestoDb(vectorizedImages);
            //await imageVectorSearchService.SaveImagesAsync(vectorizedImages);
            await imageVectorSearchService.SaveImagesInElasticSearchAsync(vectorizedImages);

            Snackbar.Configuration.PositionClass = Defaults.Classes.Position.TopRight;
            Snackbar.Add("File uploaded!", Severity.Success);
        }
        catch (Exception e)
        {
            Snackbar.Configuration.PositionClass = Defaults.Classes.Position.TopRight;
            Snackbar.Add($"Error: {e.Message}", Severity.Warning);
        }
        finally
        {
            isUploading = false;
        }

        await ClearAsync();
        images = await imageVectorSearchService.GetAllImagesFromElasticAsync();
    }

    private async Task SearchImages(EditContext context)
    {
        isSearching = true;

        // Show all results if search prompt is empty
        if (string.IsNullOrEmpty(model.SearchPrompt))
        {
            images = await imageVectorSearchService.GetAllImagesFromElasticAsync();
        }
        else
        {
            // Vectorize the user search prompt
            //var vectorizedText = await imageVectorSearchService.VectorizeText(model.SearchPrompt);

            // Run a vector search using the search prompt embedding
            @* var searchedImages = await imageVectorSearchService.SearchImagesAsync(
                model.SearchPrompt, Convert.ToUInt64(model.ResultLimit)); *@
            var searchedImages = await imageVectorSearchService.SearchImagesInElasticAsync(
                model.SearchPrompt, model.TextSearchType, model.ResultLimit);
            images = searchedImages;//.Where(x => x.Score > model.ConfidenceThreshold).ToList();
        }

        isSearching = false;
    }

    private void OnImageClick(ImageSearchItem image)
    {
        if (selectedImage == image)
        {
            // If clicking the same image, deselect it and close drawer
            selectedImage = null;
            isDrawerOpen = false;
        }
        else
        {
            // Select the new image and open drawer
            selectedImage = image;
            isDrawerOpen = true;
        }
    }

    private void OnDrawerOpenChanged(bool isOpen)
    {
        isDrawerOpen = isOpen;
        if (!isOpen)
        {
            selectedImage = null;
        }
    }

    #region UIHelpers
    private async Task ClearAsync()
    {
        await (_fileUpload?.ClearAsync() ?? Task.CompletedTask);
        _fileNames.Clear();
        ClearDragClass();
    }

    private Task OpenFilePickerAsync()
        => _fileUpload?.OpenFilePickerAsync() ?? Task.CompletedTask;

    private void OnInputFileChanged(InputFileChangeEventArgs e)
    {
        ClearDragClass();
        ImageUploads = e.GetMultipleFiles();
        foreach (var file in ImageUploads)
        {
            _fileNames.Add(file.Name);
        }
    }

    private void SetDragClass()
        => _dragClass = $"{DefaultDragClass} mud-border-primary";

    private void ClearDragClass()
        => _dragClass = DefaultDragClass;

    private async Task ResetUI()
    {
        images = await imageVectorSearchService.GetAllImagesFromElasticAsync();
    }
    #endregion
}