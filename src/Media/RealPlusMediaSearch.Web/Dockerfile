# Base image for runtime
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
USER $APP_UID
WORKDIR /app
EXPOSE 8080
EXPOSE 8081

# Build image
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src

# Copy csproj files and restore dependencies
COPY ["RealPlusMediaSearch.Web/RealPlusMediaSearch.Web.csproj", "RealPlusMediaSearch.Web/"]
RUN dotnet restore "RealPlusMediaSearch.Web/RealPlusMediaSearch.Web.csproj"

# Copy everything else and build
COPY . .
RUN dotnet build "RealPlusMediaSearch.Web/RealPlusMediaSearch.Web.csproj" -c $BUILD_CONFIGURATION -o /app/build

# Publish stage
FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "RealPlusMediaSearch.Web/RealPlusMediaSearch.Web.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

# Final stage
FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "RealPlusMediaSearch.Web.dll"]
