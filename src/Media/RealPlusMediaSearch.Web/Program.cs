using Azure;
using Azure.AI.Vision.ImageAnalysis;
using MudBlazor.Services;
using Qdrant.Client;
using RealPlusMediaSearch.Web.Components;
using RealPlusMediaSearch.Web.Services;
using RealPlusMediaSearch.Web;
using Azure.AI.OpenAI;
using Azure.Identity;
using Microsoft.Extensions.AI;
using Elastic.Clients.Elasticsearch;
using Elastic.Transport;

var builder = WebApplication.CreateBuilder(args);

// Add MudBlazor services
builder.Services.AddMudServices();

builder.Services.AddScoped(sp => new ImageAnalysisClient(
    new Uri("https://cv-media-dev.cognitiveservices.azure.com/"),
    new AzureKeyCredential("21JxX8ZdX1n4gRPjHWK3uzibgmJfMHt0gHArIIIFQWch24mlGTzvJQQJ99BDACYeBjFXJ3w3AAAFACOG1w3X"))
);
builder.Services.AddScoped(sp => new QdrantClient(
    host: "ca6ce12c-9371-4ac4-bac6-80ca53ec7e4d.europe-west3-0.gcp.cloud.qdrant.io",
    https: true,
    apiKey: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY2Nlc3MiOiJtIiwiZXhwIjoxNzQ2MzY3OTY5fQ.FI9ds-ChmoGd-ElScvjacZ_AWlkB5KncUmqUuUnTkGQ")
);
builder.Services.AddHttpClient<ImageVectorSearchService>(client =>
{
    client.BaseAddress = new Uri("https://cv-media-dev.cognitiveservices.azure.com/");
    client.DefaultRequestHeaders.Add("Ocp-Apim-Subscription-Key", "21JxX8ZdX1n4gRPjHWK3uzibgmJfMHt0gHArIIIFQWch24mlGTzvJQQJ99BDACYeBjFXJ3w3AAAFACOG1w3X");
});

builder.Services.AddSingleton(sp =>
{
    var settings = new ElasticsearchClientSettings(new Uri("https://rpls-dev-vector.es.eastus2.azure.elastic-cloud.com"))
        .Authentication(new BasicAuthentication("elastic", "Npgpn8W2uFC8N2myxbWSWacA"))
        .DefaultMappingFor<ListingMediaDocument>(m => m.IndexName("listing-media"));

    return new ElasticsearchClient(settings);
});

builder.Services.AddEmbeddingGenerator(
    new AzureOpenAIClient(
        new Uri("https://ai-foundry-dev.cognitiveservices.azure.com/"),
        new DefaultAzureCredential())
    .GetEmbeddingClient("text-embedding-ada-002")
    .AsIEmbeddingGenerator());

// Add services to the container.
builder.Services
    .AddRazorComponents()
    .AddInteractiveServerComponents();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error", createScopeForErrors: true);
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();

app.UseAntiforgery();

app.MapStaticAssets();
app.MapRazorComponents<App>()
    .AddInteractiveServerRenderMode();

app.Run();
