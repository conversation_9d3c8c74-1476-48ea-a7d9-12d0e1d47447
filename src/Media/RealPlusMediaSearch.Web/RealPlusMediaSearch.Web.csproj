<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>3de0229a-b403-4e3e-a42a-46d49aa1c590</UserSecretsId>
    <Platforms>AnyCPU;x64</Platforms>
  </PropertyGroup>


  <ItemGroup>
    <PackageReference Include="Azure.AI.Vision.ImageAnalysis" Version="1.0.0" />
    <PackageReference Include="Azure.Storage.Blobs" Version="12.24.0" />
    <PackageReference Include="Microsoft.ML.OnnxRuntime" Version="1.22.0" />
    <PackageReference Include="MudBlazor" Version="8.6.0" />
	  <PackageReference Include="Qdrant.Client" Version="1.14.0" />
	  <PackageReference Include="Microsoft.Extensions.AI" Version="9.5.0-preview.1.25262.9" />
	  <PackageReference Include="Microsoft.Extensions.AI.OpenAI" Version="9.5.0-preview.1.25262.9" />
	  <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="9.0.5" />
	  <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.5" />
	  <PackageReference Include="Azure.AI.OpenAI" Version="2.1.0" />
	  <PackageReference Include="Azure.Identity" Version="1.14.0" />
	  <PackageReference Include="Elastic.Clients.Elasticsearch" Version="8.18.0" />
  </ItemGroup>


  <ItemGroup>
    <Folder Include="wwwroot\images\" />
  </ItemGroup>
</Project>