<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>7a394042-f947-4190-967c-10ed4d3bff2e</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerfileContext>.</DockerfileContext>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Dapper" Version="2.1.66" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="6.0.2" />
    <PackageReference Include="ModelContextProtocol.AspNetCore" Version="0.1.0-preview.14" />
	<PackageReference Include="OpenTelemetry.Exporter.Console" Version="1.12.0" />
	<PackageReference Include="OpenTelemetry.Extensions.Hosting" Version="1.12.0" />
	<PackageReference Include="OpenTelemetry.Instrumentation.AspNetCore" Version="1.12.0" />
	<PackageReference Include="OpenTelemetry.Instrumentation.Http" Version="1.12.0" />
	<PackageReference Include="OpenTelemetry.Instrumentation.Runtime" Version="1.12.0" />
	<PackageReference Include="OpenTelemetry.Exporter.OpenTelemetryProtocol" Version="1.12.0" />
	<PackageReference Include="Azure.Monitor.OpenTelemetry.AspNetCore" Version="1.3.0" />
	<PackageReference Include="Mapster" Version="7.4.0" />
	<PackageReference Include="Mapster.DependencyInjection" Version="1.0.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\RealPlusMCP.Elasticsearch\RealPlusMCP.Elasticsearch.csproj" />
  </ItemGroup>

</Project>
