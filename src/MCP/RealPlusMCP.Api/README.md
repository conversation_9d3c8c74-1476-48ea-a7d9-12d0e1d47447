# RealPlus MCP Server Installation

## Claude Desktop Installation

Here are step-by-step instructions for installing Claude Desktop:
### System Requirements

macOS 10.15 or later, or Windows 10/11
Internet connection for authentication and usage

### Installation Steps
#### For macOS:

##### Download the installer
- Visit the official Anthropic website or authorized distribution channel
- Download the Claude Desktop .dmg file for macOS

##### Install the application

- Double-click the downloaded .dmg file
- Drag the Claude Desktop app to your 
- Applications folder
- Wait for the copy process to complete

##### Launch Claude Desktop

- Open your Applications folder
- Double-click Claude Desktop to launch
- If prompted about security, go to System Preferences > Security & Privacy and allow the app to run

#### For Windows:

##### Download the installer

- Visit the official Anthropic website or authorized distribution channel
- Download the Claude Desktop .exe installer for Windows

##### Run the installer

- Double-click the downloaded .exe file
- Follow the installation wizard prompts
- Choose your installation directory (default is usually fine)
- Complete the installation process

##### Launch Claude Desktop

- Find <PERSON> in your Start menu or desktop shortcut
- Double-click to launch the application

## Node.js Installation Guide

### Installation Steps

#### For Windows:

##### Download the installer
- Go to https://nodejs.org
- Download the LTS (Long Term Support) version - it's more stable
- Choose the Windows Installer (.msi) for your system (x64 for 64-bit, x86 for 32-bit)

##### Run the installer
- Double-click the downloaded .msi file
- Click "Next" through the setup wizard
- Accept the license agreement
- Choose installation directory (default is usually fine: C:\Program Files\nodejs\)
- Select components to install (keep default selections)
- Click "Install" and wait for completion

#### For macOS:

##### Download the installer
- Go to https://nodejs.org
- Download the LTS version
- Choose the macOS Installer (.pkg)

##### Run the installer
- Double-click the downloaded .pkg file
- Follow the installation wizard
- Enter your admin password when prompted
- Click "Install" and wait for completion

## Adding MCP Server to Claude Desktop

### Prerequisites
Before you begin, ensure you have:

- Claude Desktop installed and running
- Node.js installed (version 16 or higher)
- Administrative access to your computer

### Installation Steps

1. Open Claude Desktop
2. Go to Settings menu
3. In Settings dialog click Developer tab
4. Click Edit Config button
5. In File Explorer open selected 'claude_desktop_config.json' in any editor and put there next JSON configuration: {
  "mcpServers": {
    "realplus-mcp-server": {
      "command": "npx",
      "args": [
        "mcp-remote",
        "https://ca-mcp-api-dev.orangefield-593414ae.eastus2.azurecontainerapps.io/sse",
        "--transport",
        "sse-only"
      ]
    }
  }
}
6. Restart Claude Desktop