﻿namespace RealPlusMCP.Api.Models;

public sealed class SearchCriteriaViewModel
{
    // OMNI HEADER
    public string omniHeader { get; set; }

    // QUICK TAB PARAMETERS
    public int listingCategoryId { get; set; } // SALE/RENTAL/BUILDING, etc.
    public string listingCategorystatus { get; set; } // SALE/RENTAL STATUSES
    public bool includeVerifiedLisingsOnly { get; set; }
    public bool includeFinancedDealsOnly { get; set; }
    public bool includeDealsWithoutFinancingOnly { get; set; }
    public bool includeAlsoRentSaleListing { get; set; }
    public string quickSearchInfo { get; set; } // QUICK TAB QUICK TXT
    public string omniAgentOrCompanySearchInfo { get; set; } // QUICK TAB AGENT OR COMPANY TXT

    public string tempAddressOrBuildingIds { get; set; }
    public string tempWebOrListingNum { get; set; }
    public string tempOminExclusiveFirm { get; set; }
    public string tempAreaOrNeighborhoodIds { get; set; }
    public string tempSearchByExclusiveAgent { get; set; }

    public string pricemin { get; set; } // PRICEMIN/RENTMIN
    public string pricemax { get; set; } // PRICEMAX/RENTMAX
    public string bedrooms { get; set; }
    public string maxbedrooms { get; set; }
    public string bathrooms { get; set; }
    public string maxbathrooms { get; set; }
    public bool bedroomsPlus { get; set; }
    public bool bathroomsPlus { get; set; }
    public bool roomsPlus { get; set; }

    // ADVANCE ESSENTIAL TAB PARAMETERS
    public string ownershipType { get; set; } // OWNERSHIP TYPES
    public double rooms { get; set; } // TOTAL ROOMS
    public double maxrooms { get; set; } // TOTAL ROOMS
    public double minMonthlyExp { get; set; } // MIN MONTHLY EXPENSES
    public double maxMonthlyExp { get; set; } // MAX MONTHLY EXPENSES
    public bool includeMortgagePayment { get; set; }
    public double? downPayment { get; set; }
    public double? interestRate { get; set; }
    public double? termInYears { get; set; }
    public bool isAmount { get; set; }
    public string leaseTerm { get; set; }
    public string addressOrBuildingName { get; set; }
    public string addressOrBuildingIds { get; set; } // ESSENTIAL TABS TXT
    public string[] schoolNames { get; set; }
    public string[] schoolIds { get; set; }
    public string[] schoolIdsWithZone { get; set; }
    public string[] lineNames { get; set; }
    public string[] lineIds { get; set; }
    public string unitNo { get; set; } // UNIT NO.
    public string webOrListingNum { get; set; } // WEB ID/LISTING NO. TXT
    public string areaOrNeighborhood { get; set; }
    public int listedAndUpdatedActivity { get; set; }
    public DateTime? listUpdateStartDate { get; set; } // LISTUPDATED DATE START DATE
    public DateTime? listUpdateEndDate { get; set; } // LISTUPDATED DATE START DATE
    public DateTime? listStartDate { get; set; } // LIST DATE START DATE
    public DateTime? listEndDate { get; set; } // LIST DATE START DATE
    public DateTime? updateStartDate { get; set; } // UPDATED DATE START DATE
    public DateTime? updateEndDate { get; set; } // UPDATED DATE START DATE
    public string buildingPeriods { get; set; }
    public string miscellaneousEssentials { get; set; }
    public string furnishedOptions { get; set; }
    public string fees { get; set; }
    public string feesCombined { get; set; }
    public bool? freeRent { get; set; }
    public string collectYourOwnFee { get; set; }
    public string leaseTerms { get; set; }
    public string guarantors { get; set; }

    // ADVANCE APARTMENT DETAILS TAB PARAMETERS
    public bool incMissingSqFt { get; set; }
    public double minSqft { get; set; }
    public double maxSqft { get; set; }
    public double minPriceSqFt { get; set; }
    public double maxPriceSqFt { get; set; }
    public string miscellaneousAptDtls { get; set; }
    public string apartmentType { get; set; }
    public string outdoorSpace { get; set; }
    public string views { get; set; }
    public string exposure { get; set; }
    public string multifloor { get; set; }
    public string keywordSearch { get; set; }
    public string minLeaseTerm { get; set; }
    public string maxLeaseTerm { get; set; }
    public string floorMin { get; set; }
    public string floorMax { get; set; }
    public bool incPenthouse { get; set; }

    // ADVANCE TOWNHOUSE/HOME DETAILS TAB PARAMETERS
    public int minYearBuilt { get; set; }
    public int maxYearBuilt { get; set; }
    public double minWidths { get; set; }
    public double maxWidths { get; set; }
    public double? minUnits { get; set; }
    public double? maxUnits { get; set; }
    public int? minBuildingFloors { get; set; }
    public int? maxBuildingFloors { get; set; }
    public string townHouseTypes { get; set; }
    public string townHouseFeatures { get; set; }
    public string homeTypes { get; set; }
    public string homeFeatures { get; set; }

    // ADVANCE LISTING DETAILS TAB PARAMETERS
    public string listingStatus { get; set; }
    public string listingType { get; set; }
    public string offices { get; set; }
    public string listingCreditType { get; set; }
    public string leaseTypeRental { get; set; }
    public string listingActivity { get; set; }
    public DateTime? listedStartDate { get; set; }
    public DateTime? listedEndDate { get; set; }
    public string searchByExclusiveAgent { get; set; }
    public string searchByExclusiveFirm { get; set; }
    public string teams { get; set; }
    public string IncreaseOrDrop { get; set; }
    public DateTime? PriceDateStart { get; set; }
    public DateTime? PriceDateEnd { get; set; }
    public int priceChangedRangeFlag { get; set; }
    public double MinPercentChange { get; set; }
    public double MaxPercentChange { get; set; }
    public string AgentIDs { get; set; }
    public string AgentNames { get; set; }
    public string AgentTags { get; set; }
    public string ContactPhoneTags { get; set; }

    // ADVANCE BUILDING DETAILS TAB PARAMETERS
    public string buildingType { get; set; }
    public string attendedLobby { get; set; }
    public string amenities { get; set; }
    public string buildingAllows { get; set; }
    public string buildingNotAllows { get; set; }
    public string terraHoldingsManagement { get; set; }
    public string marketedByCompanies { get; set; }
    public string searchByManagingAgent { get; set; }

    // ADVANCE LOCATION DETAILS TAB PARAMETERS
    public string zipcode { get; set; }

    public string north1Location { get; set; }
    public string north2Location { get; set; }
    public string west1Location { get; set; }
    public string west2Location { get; set; }
    public string east1Location { get; set; }
    public string east2Location { get; set; }
    public string south1Location { get; set; }
    public string south2Location { get; set; }

    // LISTING DETAILS PARAMETERS [CONTRACT/LEASE SIGNED]
    public DateTime? contractSignedStartDate { get; set; }
    public DateTime? contractSignedEndDate { get; set; }

    // ADVANCE ESSENTIALS TAB PARAMETERS
    public DateTime? availableStartDate { get; set; }
    public DateTime? availableEndDate { get; set; }

    // ADVANCE OPEN HOUSE DETAILS TAB PARAMETERS
    public DateTime? openHouseStartDate { get; set; }
    public DateTime? openHouseEndDate { get; set; }

    // SOLD TAB PARAMETERS
    public DateTime? soldStartDate { get; set; }
    public DateTime? soldEndDate { get; set; }

    public DateTime? priceChangeStartDate { get; set; }
    public DateTime? priceChangeEndDate { get; set; }

    // Save Search Popup
    public string Detail { get; set; }
    public bool IncludeRecentMatches { get; set; }
    public bool EmailRecentMatches { get; set; }
    public bool EmailCustomerRecentMatches { get; set; }
    public bool IncludePreviousMatches { get; set; }
    public string FullName { get; set; }
    public int CustomerID { get; set; }
    public int PrimaryCustomerId { get; set; }
    public int CreatorId { get; set; }
    public string CreatorName { get; set; }

    public int agentSavedCriteriaID { get; set; }

    // Map Parameters
    public string mapLocation { get; set; }
    public string selectedNeighborhoodFromMap { get; set; }
    public string circleRadius { get; set; }

    // CalendarFlags
    public int? openHouseRangeFlag { get; set; }
    public int? soldRangeFlag { get; set; }
    public int? listedUpdatedRangeFlag { get; set; }
    public int? contractSignedRangeFlag { get; set; }
    public int? availableDateRangeFlag { get; set; }
    public int? priceChangeDateRangeFlag { get; set; }

    public string activeTab { get; set; }
    public string activeSubTab { get; set; }
    public bool isOnlyLandLeaseIncluded { get; set; }
    public bool isLandLeaseExcluded { get; set; }
    public bool includeNominalSales { get; set; }
    public bool includeOtherApartmentTypes { get; set; }
    public string[] SecureGroups { get; set; }
    public double? minAllowedFinancingPercent { get; set; }
    public double? maxAllowedFinancingPercent { get; set; }
    public bool? resale { get; set; }
    public int[] constructionType { get; set; }
    public int[] commonOutdoorSpace { get; set; }
    public bool? mediaHasPhotos { get; set; }
    public bool? mediaHasFloorplan { get; set; }
    public bool? mediaHasVideos { get; set; }
    public bool? mediaHasVirtualTours { get; set; }
    public string[] managementCompanies { get; set; }
    public string[] owners { get; set; }
    public string[] landlords { get; set; }
    public bool? dealBuyerAgent { get; set; }
    public bool? dealSellerAgent { get; set; }

    public bool? PostNewMatchesToCustomerCollaboration { get; set; }

    public string coBrokeAgreement { get; set; }
    public string keyLocations { get; set; }

    public string panels { get; set; }
    public string selectedTab { get; set; }

    public int privateOutdoorSpaceSearchClause { get; set; }
    public int attendedLobbySearchClause { get; set; }
    public int viewsSearchClause { get; set; }

    public string openHouseType { get; set; }
}
