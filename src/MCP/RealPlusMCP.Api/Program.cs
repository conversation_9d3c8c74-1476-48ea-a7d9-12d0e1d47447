using Azure.Monitor.OpenTelemetry.AspNetCore;
using Dapper;
using Mapster;
using Microsoft.Data.SqlClient;
using OpenTelemetry;
using OpenTelemetry.Metrics;
using OpenTelemetry.Trace;
using RealPlusNLP.Elasticsearch;
using System.Reflection;

var builder = WebApplication.CreateBuilder(args);
{
    builder.Configuration.AddEnvironmentVariables();

    builder.Services
        .AddElasticsearchServices(builder.Configuration);

    var mapsterConfig = TypeAdapterConfig.GlobalSettings;
    mapsterConfig.Scan(Assembly.GetExecutingAssembly());
    builder.Services.AddSingleton(mapsterConfig);
    builder.Services.AddMapster();

    builder.Services
        .AddMcpServer()
        .WithHttpTransport()
#if DEBUG
        .WithStdioServerTransport()
#endif
        .WithToolsFromAssembly();

    builder.Services
        .AddOpenTelemetry()
#if !DEBUG
            .UseAzureMonitor(cfg =>
            {
                    cfg.ConnectionString = builder.Configuration["APPLICATIONINSIGHTS_CONNECTION_STRING"];
            })
#endif
        .WithTracing(b => b.AddSource("*")
            .AddAspNetCoreInstrumentation()
            .AddHttpClientInstrumentation())
        .WithMetrics(b => b.AddMeter("*")
            .AddAspNetCoreInstrumentation()
            .AddHttpClientInstrumentation())
        .WithLogging()
        .UseOtlpExporter();
}

var app = builder.Build();
{
    //app.UseHttpsRedirection();
    //app.UseFileServer();
    app.MapMcp();
    //app.MapMcp("/mcp");
    //app.MapGet("/", () => "MCP Server is running...");
    // app.MapGet("test_db", async (IConfiguration configuration) =>
    // {
    //     var connectionString = "data source=terraprodsql180.airpear.net;initial catalog=RealPlusV2;user id=rpDev;password=*********;TrustServerCertificate=True;";

    //     // Create database connection using Dapper
    //     using var connection = new SqlConnection(connectionString);

    //     // Execute a simple query to test the connection with Dapper
    //     var result = await connection.QueryFirstAsync<int>("SELECT 1 AS TestResult");

    //     return TypedResults.Ok($"DB Connection Ok - Query Result: {result}");
    // });
}

app.Run();
