using System.ComponentModel;
using Dapper;
using Microsoft.Data.SqlClient;
using ModelContextProtocol.Server;
using RealPlusMCP.Api.Common;

namespace RealPlusMCP.Api.Tools;

[McpServerToolType]
public class ClientTool
{
    [McpServerTool(Name = "get_clients"),
         Description("Get clients/companies list.")]
    public async Task<IReadOnlyCollection<ClientModel>> GetClients(
        IConfiguration configuration,
        ILogger<ClientTool> logger,
        [Description("Deployment type, i.e. the data source for the clients/companies list. Use DeploymentType.Resource if nothing is specified.")]
        DeploymentType deploymentType = DeploymentType.Resource)
    {
        var connectionString = deploymentType == DeploymentType.Resource
            ? configuration.GetConnectionString("ResourceEntities")
            : configuration.GetConnectionString("AanycEntities")
            ?? throw new InvalidOperationException("Connection string not found.");

        // Create database connection
        using var connection = new SqlConnection(connectionString);

        try
        {
            var sql = @"
                select c.companycode as Code,
                    c.companyname as Name
                from tbCompany c
                inner join tbCompanyPreference cp on cp.CompanyId = c.CompanyId
                where c.client = 1 and c.IsDeleted = 0
            ";

            var dbResults = await connection.QueryAsync<ClientModel>(
                sql
            );

            return dbResults.ToList().AsReadOnly();
        }
        catch (SqlException ex)
        {
            logger.LogError(ex, "Failed to get the list of the clients");

            return Array.Empty<ClientModel>().AsReadOnly();
        }
    }
}

public sealed record ClientModel(
    [property: Description("The code of the client/company")] string Code,
    [property: Description("The name of the client/company")] string Name);