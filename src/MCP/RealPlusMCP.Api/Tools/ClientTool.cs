using System.ComponentModel;
using ModelContextProtocol.Server;

namespace RealPlusMCP.Api.Tools;

[McpServerToolType,
    Description("Get RealPlus clients information and analytics.")]
public class ClientTool
{
    [McpServerTool(Name = "get_clients"),
         Description("Get clients information and analytics.")]
    public static ClientModel GetClients(
        ILogger<ClientTool> logger)
    {
        logger.LogInformation("GetClients called");

        return new ClientModel();
    }

    public class ClientModel
    {
        public ClientModel()
        {
        }
    }
}
