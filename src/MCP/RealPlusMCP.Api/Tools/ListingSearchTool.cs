using Mapster;
using ModelContextProtocol.Server;
using RealPlusMCP.Api.Models;
using RealPlusNLP.Elasticsearch.Contracts;
using RealPlusNLP.Elasticsearch.Messages;
using System.ComponentModel;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;

namespace RealPlusMCP.Api.Tools;

[McpServerToolType,
    Description("Search listings with NLP.")]
public sealed class ListingSearchTool
{
    private static readonly string[] PublicSecureGroups =
        ["ACRIS", "NEST", "ONEKEY", "PUBLIC", "RLS", "RPDATADEPT", "STREETEASY"];

    [McpServerTool(Name = "search_listings"),
         Description("Search listings with NLP. Returns a list of found listings.")]
    public async Task<SearchResultModel<ListingModel>> SearchListings(
        IElasticSearchService elasticSearchService,
        [Description("The listings search criteria such as '1-3 bedroom and min 2 bathroom condo in All Midtown with no more then 1000 sqft'")]
        string searchText)
    {
        var nlpResult = await GetNlpResponse(searchText);
        var viewOptions = nlpResult!.SearchOptions;
        viewOptions.SecureGroups = PublicSecureGroups;
        var esOptions = viewOptions.Adapt<ElasticSearchOptions>();
        esOptions.SecureGroup = "CORC";

        var result = await elasticSearchService.GetListingSearchResultsAsync(esOptions);

        return new SearchResultModel<ListingModel>(
            result.ListingsData.Adapt<List<ListingModel>>(),
            nlpResult.UnprocessedCriteria);
    }

    private static async Task<QueryToSearchOptionsApiResponse?> GetNlpResponse(string text)
    {
        var content = new { query = text };

        var client = new HttpClient();
        var request = new HttpRequestMessage
        {
            Method = HttpMethod.Post,
            RequestUri = new Uri("https://ca-nlp-api-dev.orangefield-593414ae.eastus2.azurecontainerapps.io/listings/query-to-search-options"),
            Headers =
            {
                { "X-Api-Key", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJlMzYwZjIwZS1mZjYwLTQwZjYtYjIwZi1mZjYwZjYwZjYwZjAiLCJpYXQiOjE1NzYwNjYwNzcsImp0aSI6IjIwMjAtMDItMjJUMDk6MzA6MjcuNjYwNjA2WiIsImV4cCI6MTU3NjA2NjA3OH0.1" },
            },
            Content = new StringContent(JsonSerializer.Serialize(content), Encoding.UTF8, "application/json")
            {
                Headers =
                {
                    ContentType = new MediaTypeHeaderValue("application/json")
                }
            }
        };

        using var response = await client.SendAsync(request);
        response.EnsureSuccessStatusCode();

        return await response.Content.ReadFromJsonAsync<QueryToSearchOptionsApiResponse>();
    }

    record QueryToSearchOptionsApiResponse(
        SearchCriteriaViewModel SearchOptions,
        string? UnprocessedCriteria);
}

public sealed record SearchResultModel<TModel>(
    IReadOnlyCollection<TModel> Listings,
    [property: Description("Search text that wasn't recognized by LLM")]
    string? UnprocessedCriteria);