using Dapper;
using Microsoft.Data.SqlClient;
using ModelContextProtocol.Server;
using System.ComponentModel;
using System.Data;
using System.Text.Json.Serialization;

namespace RealPlusMCP.Api.Tools;

[McpServerToolType,
    Description("Reconcile a listing’s media: compares each original asset (media origin) with its processed asset in central media storage and reports mismatches or missing files.")]
public sealed class MediaTool
{
    [McpServerTool(Name = "compare_media"),
         Description("Return a reconciliation report for one listing. Input = listingNumber, deploymentType. Output = array of items { differenceFlag, originUrl, storageUrl }.")]
    public static async Task<IReadOnlyCollection<CompareMediaModel>> CompareMedia(
        IConfiguration configuration,
        ILogger<MediaTool> logger,
        string listingNumber,
        [Description("The deployment type. Use DeploymentType.Resource if nothing is specified.")]
        DeploymentType deploymentType = DeploymentType.Resource)
    {
        var connectionString = deploymentType == DeploymentType.Resource
            ? configuration.GetConnectionString("ResourceEntities")
            : configuration.GetConnectionString("AanycEntities")
            ?? throw new InvalidOperationException("Connection string 'RealPlusEntities' not found.");

        // Create database connection
        using var connection = new SqlConnection(connectionString);

        try
        {
            // Execute a SQL query that gets the listingId and then passes it to the stored procedure
            var sql = @"
                DECLARE @ListingId INT;

                -- Get the listingId from the listings table using the listingNumber
                SELECT @ListingId = listingId FROM listings WHERE listingnumber = @ListingNumber;

                IF @ListingId IS NULL
                BEGIN
                    THROW 50000, 'No listing found with the specified listing number', 1;
                    RETURN;
                END

                -- Execute the stored procedure with the listingId as a string
                DECLARE @ListingIdStr VARCHAR(50) = CAST(@ListingId AS VARCHAR(50));
                EXEC utl.utlCompareMediaByListingID @listingId = @ListingIdStr;
            ";

            // Execute the SQL query and get results mapped to CompareMediaEntity
            var dbResults = await connection.QueryAsync<CompareMediaEntity>(
                sql,
                new { listingNumber }
            );

            // If no results were returned, the listing might not have any media
            if (!dbResults.Any())
            {
                logger.LogWarning("No media found for listing number: {listingNumber}", listingNumber);
                return Array.Empty<CompareMediaModel>().AsReadOnly();
            }

            return dbResults.Select(r => new CompareMediaModel(
                DifferenceFlag: r.Difference > 0,
                OriginUrl: r.MediaUrl,
                StorageUrl: r.ImaginationUrl
            )).ToList().AsReadOnly();
        }
        catch (SqlException ex)
        {
            if (ex.Message.Contains("No listing found"))
            {
                logger.LogWarning("No listing found with the specified listing number: {listingNumber}", listingNumber);
            }
            else
            {
                logger.LogError(ex, "Error comparing media for listing number: {listingNumber}", listingNumber);
            }

            return Array.Empty<CompareMediaModel>().AsReadOnly();
        }
    }

    // Internal record for database mapping
    private sealed record CompareMediaEntity(
        int Difference,
        int ListingID,
        string? MediaUrl,
        string? ImaginationUrl
    );
}

// Public record for API response
public sealed record CompareMediaModel(
    [property: Description("true if the asset is missing from either dataset or the URLs differ; false if identical.")]
    bool DifferenceFlag,
    [property: Description("URL of the original media asset received from the listing feed (media origin). Null if no origin asset exists.")]
    string? OriginUrl,
    [property: Description("URL of the processed media stored in the central media storage (e.g., Imgix). Null if not yet processed or mismatched with the origin.")]
    string? StorageUrl
);

[JsonConverter(typeof(JsonStringEnumConverter))]
[Description("Deployment type, i.e. the data source of the media comparison")]
public enum DeploymentType
{
    Resource,
    [Description("Aanyc or AllAccessNYC")]
    Aanyc
}