﻿using Mapster;
using RealPlusMCP.Api.Extensions;
using RealPlusMCP.Api.Models;
using RealPlusNLP.Elasticsearch.Messages;

namespace RealPlusMCP.Api.Mappings;

public sealed class SearchCriteriaViewToESMappingConfig : IRegister
{
    private static readonly int[] activeStatuses =
        [163, 3593, 3594, 3688, 3595, 3687, 3596, 3689, 3848];

    public void Register(TypeAdapterConfig config)
    {
        config.NewConfig<SearchCriteriaViewModel, ElasticSearchOptions>()
            .Map(dest => dest.CategoryId, src => src.listingCategoryId)
            .Map(dest => dest.BuildingPeriods, src => src.buildingPeriods.AttributeValuesAsIntegers())
            .Map(dest => dest.OwnershipIds, src => src.ownershipType.AttributeValuesAsIntegers())
            .Map(dest => dest.Amenities, src => src.amenities.AttributeValuesAsIntegers())
            .Map(dest => dest.AttendedLobby, src => src.attendedLobby.AttributeValuesAsIntegers())
            .Map(dest => dest.BedroomsMin, src =>
                string.IsNullOrEmpty(src.bedrooms) ? 0 : int.Parse(src.bedrooms))
            .Map(dest => dest.BedroomsMax, src =>
                string.IsNullOrEmpty(src.maxbedrooms) ? 0 : int.Parse(src.maxbedrooms))
            .Map(dest => dest.BedroomsMore, src => src.bedroomsPlus)
            .Map(dest => dest.BathroomsMin, src =>
                string.IsNullOrEmpty(src.bathrooms) ? 0 : double.Parse(src.bathrooms))
            .Map(dest => dest.BathroomsMax, src =>
                string.IsNullOrEmpty(src.maxbathrooms) ? 0 : double.Parse(src.maxbathrooms))
            .Map(dest => dest.BathroomsMore, src => src.bathroomsPlus)
            .Map(dest => dest.TotalRoomsMin, src => src.rooms)
            .Map(dest => dest.TotalRoomsMax, src => src.maxrooms)
            .Map(dest => dest.TotalRoomsMore, src => src.roomsPlus)
            .Map(dest => dest.PriceMin, src =>
                string.IsNullOrEmpty(src.pricemin) ? 0 : double.Parse(src.pricemin))
            .Map(dest => dest.PriceMax, src =>
                string.IsNullOrEmpty(src.pricemax) ? 0 : double.Parse(src.pricemax))
            .Map(dest => dest.SqFtMin, src => src.minSqft)
            .Map(dest => dest.SqFtMax, src => src.maxSqft)
            .Map(dest => dest.NeighborhoodIds, src => src.areaOrNeighborhood.AttributeValuesAsIntegers())
            // defaults
            .Map(dest => dest.ChildSecureGroups, src => Array.Empty<string>())
            .Map(dest => dest.MonthlyExpensesOptions, src => new ElasticSearchOptions.MonthlyExpenses())
            .Map(dest => dest.Deals, src => new ElasticSearchOptions.DealAgent())
            .Map(dest => dest.MediaAssets, src => new ElasticSearchOptions.MediaAsset())
            .Map(dest => dest.SearchClause, src => new ElasticSearchOptions.SearchClauseOptions
            {
                PrivateOutdoorSpace = SearchClause.And,
                AttendedLobby = SearchClause.And,
                Views = SearchClause.And
            })
            .Map(dest => dest.StatusIds, src => activeStatuses);
    }
}
