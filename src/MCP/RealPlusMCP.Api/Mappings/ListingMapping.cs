﻿using Mapster;
using RealPlusMCP.Api.Models;
using RealPlusNLP.Elasticsearch.Documents;

namespace RealPlusMCP.Api.Mappings;

public sealed class ListingMapping : IRegister
{
    public void Register(TypeAdapterConfig config)
    {
        config.NewConfig<ListingDocument, ListingModel>()
            .Map(dest => dest.Status, src => src.CurrentStatus)
            .Map(dest => dest.OwnershipType, src => src.Property.OwnershipType)
            .Map(dest => dest.UnitNumber, src => src.Unit.UnitNumber)
            .Map(dest => dest.Address, src => src.Property.Address)
            .Map(dest => dest.Neighborhood, src => src.Property.Neighborhood);
    }
}
