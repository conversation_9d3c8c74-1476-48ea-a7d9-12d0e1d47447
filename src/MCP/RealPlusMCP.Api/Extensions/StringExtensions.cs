﻿namespace RealPlusMCP.Api.Extensions;

public static class StringExtensions
{
    public static int[]? AttributeValuesAsIntegers(this string strVal)
    {
        return AttributeValuesAsIntegers(strVal, ",");
    }

    public static int[]? AttributeValuesAsIntegers(this string strVal, string separator)
    {
        if (string.IsNullOrEmpty(strVal))
        {
            return null;
        }

        return [.. strVal.Split([separator], StringSplitOptions.None)
            .Where(e => e != "")
            .Select(int.Parse)];
    }
}