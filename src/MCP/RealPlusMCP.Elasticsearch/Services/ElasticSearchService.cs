﻿using Nest;
using RealPlusNLP.Elasticsearch.Contracts;
using RealPlusNLP.Elasticsearch.Documents;
using System.Text;
using Microsoft.Extensions.Options;
using RealPlusNLP.Elasticsearch.Messages;
using RealPlusNLP.Elasticsearch.Options;

namespace RealPlusNLP.Elasticsearch.Services;

public sealed class ElasticSearchService : IElasticSearchService
{
    private readonly ElasticClient client;
    private readonly IElasticSearchQueryCreator<ListingDocument> listingsQueryCreator;

    public ElasticSearchService(
        IOptions<ElasticsearchSettings> options,
        IElasticSearchQueryCreator<ListingDocument> listingsQueryCreator)
    {
        this.listingsQueryCreator = listingsQueryCreator;
        var settings = new ConnectionSettings(new Uri(options.Value.Url))
            .BasicAuthentication(options.Value.User, options.Value.Password)
            .EnableHttpCompression()
            .PrettyJson()
            .EnableDebugMode()
            .DefaultMappingFor<ListingDocument>(m => m
                .IndexName(options.Value.ListingSearchIndex)
                    .IdProperty(p => p.ListingId));

        client = new ElasticClient(settings);
    }

    public async Task<ElasticSearchResponse<ListingDocument>> GetListingSearchCountAsync(ElasticSearchOptions options)
    {
        var result = new ElasticSearchResponse<ListingDocument>();
        if (options.CategoryId == 317)
        {
            return result;
        }

        var startOn = DateTime.Now;

        var response = await client.CountAsync<ListingDocument>(s => s.Query(q => q.ConstantScore(c => c.Filter(f => f.Bool(b => b.Must(listingsQueryCreator.Create(options)))))));
        result.Count = response.Count;
        result.Took = (DateTime.Now - startOn).TotalSeconds;

        return result;
    }

    public async Task<ElasticSearchResponse<ListingDocument>> GetListingSearchResultsAsync(ElasticSearchOptions options, int size = 100)
    {
        var result = new ElasticSearchResponse<ListingDocument>();
        if (options.CategoryId == 317)
        {
            return result;
        }

        var response = await client.SearchAsync<ListingDocument>(s => s.Size(size).Query(q => q.ConstantScore(c => c.Filter(f => f.Bool(b => b.Must(listingsQueryCreator.Create(options)))))));

        result.Query = Encoding.UTF8.GetString(response.ApiCall.RequestBodyInBytes);
        result.Count = response.Total;
        result.Took = response.Took;
        result.ListingsData = [.. response.Documents];

        return result;
    }
}
