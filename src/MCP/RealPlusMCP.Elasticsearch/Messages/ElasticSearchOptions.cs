﻿using Nest;
using RealPlusNLP.Elasticsearch.Documents;
using RealPlusNLP.Elasticsearch.Utils;

namespace RealPlusNLP.Elasticsearch.Messages;

public enum SearchClause
{
    And,
    Or
}

public class ElasticSearchOptions
{
    private int? bedroomsMin;
    private int? bedroomsMax;
    private double? bathroomsMin;
    private double? bathroomsMax;
    private double? totalRoomsMin;
    private double? totalRoomsMax;
    private int? minLeaseTerm;
    private int? maxLeaseTerm;

    public int CategoryId { get; set; }

    // listing
    public int[] StatusIds { get; set; }
    public int[] OwnershipIds { get; set; }
    [Keyword]
    public string[] WebIds { get; set; }
    [Keyword]
    public string[] SchoolIdsWithZone { get; set; }
    [Keyword]
    public string[] LineIds { get; set; }

    [Keyword]
    public string[] ListingNumbers { get; set; }
    public string[] ListingCompanies { get; set; }
    public double PriceMin { get; set; }
    public double PriceMax { get; set; }
    public MonthlyExpenses MonthlyExpensesOptions { get; set; }
    public bool VerifiedOnly { get; set; }
    public bool FinancedDealsOnly { get; set; }
    public bool DealsWithoutFinancingOnly { get; set; }
    public bool IncludeAlsoRentSaleListing { get; set; }
    public bool IncludeNominalSales { get; set; }
    public bool IncludeOtherApartmentTypes { get; set; }

    [Keyword]
    public string SecureGroup { get; set; }
    public string[] ChildSecureGroups { get; set; }
    public double PricePerSqFtMin { get; set; }
    public double PricePerSqFtMax { get; set; }
    public DateTime? SoldActivityDateStart { get; set; }
    public DateTime? SoldActivityDateEnd { get; set; }
    public DateTime? ContractLeaseSignedActivityDateStart { get; set; }
    public DateTime? ContractLeaseSignedActivityDateEnd { get; set; }
    public DateTime? ListedUpdatedActivityDateStart { get; set; }
    public DateTime? ListedUpdatedActivityDateEnd { get; set; }
    public DateTime? OpenHouseDateStart { get; set; }
    public DateTime? OpenHouseDateEnd { get; set; }
    public OpenHouseType OpenHouseType { get; set; }
    public DateTime? AvailableDateStart { get; set; }
    public DateTime? AvailableDateEnd { get; set; }
    public int[] ListingTypes { get; set; }
    [Keyword]
    public string PriceChangeDirection { get; set; }
    public double PercentChangeMin { get; set; }
    public double PercentChangeMax { get; set; }
    public DateTime? PriceChangeStartDate { get; set; }
    public DateTime? PriceChangeEndDate { get; set; }
    public int[] OfficeIds { get; set; }
    public FeeOptions AdvancedFees { get; set; }
    public bool? FreeRent { get; set; }
    public bool[] CollectYourOwnFees { get; set; }
    public string[] LeaseTerms { get; set; }
    public string[] Guarantors { get; set; }
    public int[] LeaseTypes { get; set; }
    public string[] AgentTags { get; set; }
    public string[] ContactPhoneTags { get; set; }
    public string Keyword { get; set; }
    public int[] AgentIds { get; set; }
    public int ListedUpdatedActivityId { get; set; }

    // unit
    public int? BedroomsMin { get => bedroomsMin; set => bedroomsMin = value == 0 ? null : value == -1 ? 0 : value; }
    public int? BedroomsMax { get => bedroomsMax; set => bedroomsMax = value == 0 ? null : value == -1 ? 0 : value; }
    public bool BedroomsMore { get; set; }
    public double? BathroomsMin { get => bathroomsMin; set => bathroomsMin = value == 0 ? null : value; }
    public double? BathroomsMax { get => bathroomsMax; set => bathroomsMax = value == 0 ? null : value; }
    public bool BathroomsMore { get; set; }
    public double? TotalRoomsMin { get => totalRoomsMin; set => totalRoomsMin = value == 0 ? null : value; }
    public double? TotalRoomsMax { get => totalRoomsMax; set => totalRoomsMax = value == 0 ? null : value; }
    public bool TotalRoomsMore { get; set; }
    public int? MinLeaseTerm { get => minLeaseTerm; set => minLeaseTerm = value == 0 ? null : value; }
    public int? MaxLeaseTerm { get => maxLeaseTerm; set => maxLeaseTerm = value == 0 ? null : value; }
    public int[] ApartmentTypes { get; set; }
    public double SqFtMin { get; set; }
    public double SqFtMax { get; set; }
    public bool IncludeMissingSqFt { get; set; }
    public int FloorMin { get; set; }
    public int FloorMax { get; set; }
    public int[] Views { get; set; }
    public int[] Exposure { get; set; }
    public int[] MultiFloor { get; set; }
    public int[] TownhouseType { get; set; }
    public int[] TownhouseFeatures { get; set; }
    public int[] Furnished { get; set; }
    public int[] MiscellaneousDetails { get; set; }
    public int[] MiscellaneousEssentials { get; set; }
    public int[] OutdoorSpace { get; set; }
    public bool IncludePenthouse { get; set; }
    public MediaAsset MediaAssets { get; set; }

    public string CoBrokeAgreement { get; set; }

    // building
    public int[] NeighborhoodIds { get; set; }
    public AddressUnit[] AddressUnits { get; set; }
    public MapShape[] MapShapes { get; set; }
    public StreetGrid[] StreetGrids { get; set; }
    [Keyword]
    public string[] PostCodes { get; set; }
    public int[] BuildingPeriods { get; set; }
    public double WidthMin { get; set; }
    public double WidthMax { get; set; }
    public double? UnitsMin { get; set; }
    public double? UnitsMax { get; set; }

    public int? BuildingFloorsMin { get; set; }
    public int? BuildingFloorsMax { get; set; }
    public int[] BuildingTypes { get; set; }
    public int[] BuildingAllowed { get; set; }
    public int[] BuildingNotAllowed { get; set; }
    public int YearBuiltMin { get; set; }
    public int YearBuiltMax { get; set; }
    public int[] ManagedBuildings { get; set; }
    public int[] Amenities { get; set; }
    public string LandLease { get; set; }
    public int[] AttendedLobby { get; set; }

    // company allowed secure groups
    public string[] SecureGroups { get; set; }
    public double? MinAllowedFinancingPercent { get; set; }
    public double? MaxAllowedFinancingPercent { get; set; }
    public bool? Resale { get; set; }
    public int[] ConstructionTypes { get; set; }
    public int[] CommonOutdoorSpaces { get; set; }
    public string[] Owners { get; set; }
    public string[] Landlords { get; set; }
    public string[] ManagementCompanies { get; set; }
    public string[] MarketedByCompanies { get; set; }
    public DealAgent Deals { get; set; }
    public string[] KeyLocations { get; set; }
    public SearchClauseOptions SearchClause { get; set; }

    public override int GetHashCode()
    {
        var hashCode = 1341054025;
        hashCode = hashCode * -1521134295 + EqualityComparer<int?>.Default.GetHashCode(bedroomsMin);
        hashCode = hashCode * -1521134295 + EqualityComparer<int?>.Default.GetHashCode(bedroomsMax);
        hashCode = hashCode * -1521134295 + EqualityComparer<double?>.Default.GetHashCode(bathroomsMin);
        hashCode = hashCode * -1521134295 + EqualityComparer<double?>.Default.GetHashCode(bathroomsMax);
        hashCode = hashCode * -1521134295 + EqualityComparer<double?>.Default.GetHashCode(totalRoomsMin);
        hashCode = hashCode * -1521134295 + EqualityComparer<double?>.Default.GetHashCode(totalRoomsMax);
        hashCode = hashCode * -1521134295 + EqualityComparer<int?>.Default.GetHashCode(minLeaseTerm);
        hashCode = hashCode * -1521134295 + EqualityComparer<int?>.Default.GetHashCode(maxLeaseTerm);
        hashCode = hashCode * -1521134295 + CategoryId.GetHashCode();
        hashCode = hashCode * -1521134295 + ArrayEqualityComparer<int>.Default.GetHashCode(StatusIds);
        hashCode = hashCode * -1521134295 + ArrayEqualityComparer<int>.Default.GetHashCode(OwnershipIds);
        hashCode = hashCode * -1521134295 + ArrayEqualityComparer<string>.Default.GetHashCode(WebIds);
        hashCode = hashCode * -1521134295 + ArrayEqualityComparer<string>.Default.GetHashCode(SchoolIdsWithZone);
        hashCode = hashCode * -1521134295 + ArrayEqualityComparer<string>.Default.GetHashCode(LineIds);
        hashCode = hashCode * -1521134295 + ArrayEqualityComparer<string>.Default.GetHashCode(ListingNumbers);
        hashCode = hashCode * -1521134295 + ArrayEqualityComparer<string>.Default.GetHashCode(ListingCompanies);
        hashCode = hashCode * -1521134295 + PriceMin.GetHashCode();
        hashCode = hashCode * -1521134295 + PriceMax.GetHashCode();
        hashCode = hashCode * -1521134295 + EqualityComparer<double?>.Default.GetHashCode(MonthlyExpensesOptions.Min);
        hashCode = hashCode * -1521134295 + EqualityComparer<double?>.Default.GetHashCode(MonthlyExpensesOptions.Max);
        hashCode = hashCode * -1521134295 + EqualityComparer<double?>.Default.GetHashCode(MonthlyExpensesOptions.DownPayment);
        hashCode = hashCode * -1521134295 + EqualityComparer<double?>.Default.GetHashCode(MonthlyExpensesOptions.InterestRate);
        hashCode = hashCode * -1521134295 + EqualityComparer<double?>.Default.GetHashCode(MonthlyExpensesOptions.TermInYears);
        hashCode = hashCode * -1521134295 + EqualityComparer<bool?>.Default.GetHashCode(MonthlyExpensesOptions.IncludeMortgagePayment);
        hashCode = hashCode * -1521134295 + EqualityComparer<bool?>.Default.GetHashCode(MonthlyExpensesOptions.IsAmount);
        hashCode = hashCode * -1521134295 + VerifiedOnly.GetHashCode();
        hashCode = hashCode * -1521134295 + DealsWithoutFinancingOnly.GetHashCode();
        hashCode = hashCode * -1521134295 + FinancedDealsOnly.GetHashCode();
        hashCode = hashCode * -1521134295 + IncludeNominalSales.GetHashCode();
        hashCode = hashCode * -1521134295 + IncludeOtherApartmentTypes.GetHashCode();
        hashCode = hashCode * -1521134295 + IncludeAlsoRentSaleListing.GetHashCode();
        hashCode = hashCode * -1521134295 + EqualityComparer<string>.Default.GetHashCode(SecureGroup);
        hashCode = hashCode * -1521134295 + PricePerSqFtMin.GetHashCode();
        hashCode = hashCode * -1521134295 + PricePerSqFtMax.GetHashCode();
        hashCode = hashCode * -1521134295 + SoldActivityDateStart.GetHashCode();
        hashCode = hashCode * -1521134295 + SoldActivityDateEnd.GetHashCode();
        hashCode = hashCode * -1521134295 + ContractLeaseSignedActivityDateStart.GetHashCode();
        hashCode = hashCode * -1521134295 + ContractLeaseSignedActivityDateEnd.GetHashCode();
        hashCode = hashCode * -1521134295 + ListedUpdatedActivityDateStart.GetHashCode();
        hashCode = hashCode * -1521134295 + ListedUpdatedActivityDateEnd.GetHashCode();
        hashCode = hashCode * -1521134295 + OpenHouseDateStart.GetHashCode();
        hashCode = hashCode * -1521134295 + OpenHouseDateEnd.GetHashCode();
        hashCode = hashCode * -1521134295 + OpenHouseType.GetHashCode();
        hashCode = hashCode * -1521134295 + AvailableDateStart.GetHashCode();
        hashCode = hashCode * -1521134295 + AvailableDateEnd.GetHashCode();
        hashCode = hashCode * -1521134295 + ArrayEqualityComparer<int>.Default.GetHashCode(ListingTypes);
        hashCode = hashCode * -1521134295 + EqualityComparer<string>.Default.GetHashCode(PriceChangeDirection);
        hashCode = hashCode * -1521134295 + PercentChangeMin.GetHashCode();
        hashCode = hashCode * -1521134295 + PercentChangeMax.GetHashCode();
        hashCode = hashCode * -1521134295 + PriceChangeStartDate.GetHashCode();
        hashCode = hashCode * -1521134295 + PriceChangeEndDate.GetHashCode();
        hashCode = hashCode * -1521134295 + ArrayEqualityComparer<int>.Default.GetHashCode(OfficeIds);
        hashCode = hashCode * -1521134295 + ArrayEqualityComparer<int>.Default.GetHashCode(AdvancedFees?.AggregatedFees);
        hashCode = hashCode * -1521134295 + ArrayEqualityComparer<int>.Default.GetHashCode(AdvancedFees?.CombinedFees);
        hashCode = hashCode * -1521134295 + EqualityComparer<bool?>.Default.GetHashCode(FreeRent);
        hashCode = hashCode * -1521134295 + ArrayEqualityComparer<bool>.Default.GetHashCode(CollectYourOwnFees);
        hashCode = hashCode * -1521134295 + ArrayEqualityComparer<string>.Default.GetHashCode(LeaseTerms);
        hashCode = hashCode * -1521134295 + ArrayEqualityComparer<string>.Default.GetHashCode(Guarantors);
        hashCode = hashCode * -1521134295 + ArrayEqualityComparer<int>.Default.GetHashCode(LeaseTypes);
        hashCode = hashCode * -1521134295 + ArrayEqualityComparer<string>.Default.GetHashCode(AgentTags);
        hashCode = hashCode * -1521134295 + ArrayEqualityComparer<string>.Default.GetHashCode(ContactPhoneTags);
        hashCode = hashCode * -1521134295 + EqualityComparer<string>.Default.GetHashCode(Keyword);
        hashCode = hashCode * -1521134295 + ArrayEqualityComparer<int>.Default.GetHashCode(AgentIds);
        hashCode = hashCode * -1521134295 + ListedUpdatedActivityId.GetHashCode();
        hashCode = hashCode * -1521134295 + EqualityComparer<int?>.Default.GetHashCode(BedroomsMin);
        hashCode = hashCode * -1521134295 + EqualityComparer<int?>.Default.GetHashCode(BedroomsMax);
        hashCode = hashCode * -1521134295 + BedroomsMore.GetHashCode();
        hashCode = hashCode * -1521134295 + EqualityComparer<double?>.Default.GetHashCode(BathroomsMin);
        hashCode = hashCode * -1521134295 + EqualityComparer<double?>.Default.GetHashCode(BathroomsMax);
        hashCode = hashCode * -1521134295 + BathroomsMore.GetHashCode();
        hashCode = hashCode * -1521134295 + EqualityComparer<double?>.Default.GetHashCode(TotalRoomsMin);
        hashCode = hashCode * -1521134295 + EqualityComparer<double?>.Default.GetHashCode(TotalRoomsMax);
        hashCode = hashCode * -1521134295 + TotalRoomsMore.GetHashCode();
        hashCode = hashCode * -1521134295 + EqualityComparer<int?>.Default.GetHashCode(MinLeaseTerm);
        hashCode = hashCode * -1521134295 + EqualityComparer<int?>.Default.GetHashCode(MaxLeaseTerm);
        hashCode = hashCode * -1521134295 + ArrayEqualityComparer<int>.Default.GetHashCode(ApartmentTypes);
        hashCode = hashCode * -1521134295 + SqFtMin.GetHashCode();
        hashCode = hashCode * -1521134295 + SqFtMax.GetHashCode();
        hashCode = hashCode * -1521134295 + IncludeMissingSqFt.GetHashCode();
        hashCode = hashCode * -1521134295 + FloorMin.GetHashCode();
        hashCode = hashCode * -1521134295 + FloorMax.GetHashCode();
        hashCode = hashCode * -1521134295 + ArrayEqualityComparer<int>.Default.GetHashCode(Views);
        hashCode = hashCode * -1521134295 + ArrayEqualityComparer<int>.Default.GetHashCode(Exposure);
        hashCode = hashCode * -1521134295 + ArrayEqualityComparer<int>.Default.GetHashCode(MultiFloor);
        hashCode = hashCode * -1521134295 + ArrayEqualityComparer<int>.Default.GetHashCode(TownhouseType);
        hashCode = hashCode * -1521134295 + ArrayEqualityComparer<int>.Default.GetHashCode(TownhouseFeatures);
        hashCode = hashCode * -1521134295 + ArrayEqualityComparer<int>.Default.GetHashCode(Furnished);
        hashCode = hashCode * -1521134295 + ArrayEqualityComparer<int>.Default.GetHashCode(MiscellaneousDetails);
        hashCode = hashCode * -1521134295 + ArrayEqualityComparer<int>.Default.GetHashCode(MiscellaneousEssentials);
        hashCode = hashCode * -1521134295 + ArrayEqualityComparer<int>.Default.GetHashCode(OutdoorSpace);
        hashCode = hashCode * -1521134295 + IncludePenthouse.GetHashCode();
        hashCode = hashCode * -1521134295 + EqualityComparer<string>.Default.GetHashCode(CoBrokeAgreement);
        hashCode = hashCode * -1521134295 + ArrayEqualityComparer<int>.Default.GetHashCode(NeighborhoodIds);
        hashCode = hashCode * -1521134295 + ArrayEqualityComparer<AddressUnit>.Default.GetHashCode(AddressUnits);
        hashCode = hashCode * -1521134295 + ArrayEqualityComparer<MapShape>.Default.GetHashCode(MapShapes);
        hashCode = hashCode * -1521134295 + ArrayEqualityComparer<StreetGrid>.Default.GetHashCode(StreetGrids);
        hashCode = hashCode * -1521134295 + ArrayEqualityComparer<string>.Default.GetHashCode(PostCodes);
        hashCode = hashCode * -1521134295 + ArrayEqualityComparer<int>.Default.GetHashCode(BuildingPeriods);
        hashCode = hashCode * -1521134295 + WidthMin.GetHashCode();
        hashCode = hashCode * -1521134295 + WidthMax.GetHashCode();
        hashCode = hashCode * -1521134295 + UnitsMin.GetHashCode();
        hashCode = hashCode * -1521134295 + UnitsMax.GetHashCode();
        hashCode = hashCode * -1521134295 + ArrayEqualityComparer<int>.Default.GetHashCode(BuildingTypes);
        hashCode = hashCode * -1521134295 + ArrayEqualityComparer<int>.Default.GetHashCode(BuildingAllowed);
        hashCode = hashCode * -1521134295 + ArrayEqualityComparer<int>.Default.GetHashCode(BuildingNotAllowed);
        hashCode = hashCode * -1521134295 + YearBuiltMin.GetHashCode();
        hashCode = hashCode * -1521134295 + YearBuiltMax.GetHashCode();
        hashCode = hashCode * -1521134295 + ArrayEqualityComparer<int>.Default.GetHashCode(ManagedBuildings);
        hashCode = hashCode * -1521134295 + ArrayEqualityComparer<int>.Default.GetHashCode(Amenities);
        hashCode = hashCode * -1521134295 + EqualityComparer<string>.Default.GetHashCode(LandLease);
        hashCode = hashCode * -1521134295 + ArrayEqualityComparer<int>.Default.GetHashCode(AttendedLobby);
        hashCode = hashCode * -1521134295 + ArrayEqualityComparer<string>.Default.GetHashCode(SecureGroups);
        hashCode = hashCode * -1521134295 + EqualityComparer<bool?>.Default.GetHashCode(MediaAssets.HasPhotos);
        hashCode = hashCode * -1521134295 + EqualityComparer<bool?>.Default.GetHashCode(MediaAssets.HasFloorplan);
        hashCode = hashCode * -1521134295 + EqualityComparer<bool?>.Default.GetHashCode(MediaAssets.HasVideos);
        hashCode = hashCode * -1521134295 + EqualityComparer<bool?>.Default.GetHashCode(MediaAssets.HasVirtualTours);
        hashCode = hashCode * -1521134295 + EqualityComparer<double?>.Default.GetHashCode(MinAllowedFinancingPercent);
        hashCode = hashCode * -1521134295 + EqualityComparer<double?>.Default.GetHashCode(MaxAllowedFinancingPercent);
        hashCode = hashCode * -1521134295 + ArrayEqualityComparer<int>.Default.GetHashCode(ConstructionTypes);
        hashCode = hashCode * -1521134295 + ArrayEqualityComparer<int>.Default.GetHashCode(CommonOutdoorSpaces);
        hashCode = hashCode * -1521134295 + ArrayEqualityComparer<string>.Default.GetHashCode(Landlords);
        hashCode = hashCode * -1521134295 + ArrayEqualityComparer<string>.Default.GetHashCode(Owners);
        hashCode = hashCode * -1521134295 + ArrayEqualityComparer<string>.Default.GetHashCode(ManagementCompanies);
        hashCode = hashCode * -1521134295 + ArrayEqualityComparer<string>.Default.GetHashCode(MarketedByCompanies);
        hashCode = hashCode * -1521134295 + EqualityComparer<bool?>.Default.GetHashCode(Deals.BuyerAgent);
        hashCode = hashCode * -1521134295 + EqualityComparer<bool?>.Default.GetHashCode(Deals.SellerAgent);
        hashCode = hashCode * -1521134295 + ArrayEqualityComparer<string>.Default.GetHashCode(KeyLocations);
        hashCode = hashCode * -1521134295 + EqualityComparer<int?>.Default.GetHashCode(BuildingFloorsMin);
        hashCode = hashCode * -1521134295 + EqualityComparer<int?>.Default.GetHashCode(BuildingFloorsMax);

        return hashCode;
    }

    public bool HasClosedStatus()
    {
        return StatusIds?.Length > 0 && StatusIds.Any(x => new[] { 156, 3598, 220, 3602 }.Contains(x));
    }

    public bool IsSales()
    {
        return CategoryId == 8;
    }

    public bool IsRental()
    {
        return CategoryId == 7;
    }

    public string[] SecureGroupTerms()
    {
        var secureGroups = new HashSet<string> { SecureGroup };
        if (ChildSecureGroups.Length > 0)
        {
            secureGroups.UnionWith(ChildSecureGroups);
        }

        return secureGroups.ToArray();
    }

    public class AddressUnit
    {
        public int? RPBin { get; set; }
        public string Unit { get; set; }
    }

    public class StreetGrid
    {
        public int North { get; set; }
        public int South { get; set; }
        public int West { get; set; }
        public int East { get; set; }
    }

    public class MapCoordinate
    {
        public double Latitude { get; set; }
        public double Longitude { get; set; }
    }

    public class MapShape
    {
        public MapCoordinate[] Coordinates { get; set; }
    }

    public class MapCircle : MapShape
    {
        public double Radius { get; set; }
    }

    public class MediaAsset
    {
        public bool? HasPhotos { get; set; }
        public bool? HasFloorplan { get; set; }
        public bool? HasVideos { get; set; }
        public bool? HasVirtualTours { get; set; }
    }

    public class DealAgent
    {
        public bool? BuyerAgent { get; set; }
        public bool? SellerAgent { get; set; }
    }

    public class FeeOptions
    {
        public int[] AggregatedFees { get; set; }
        public int[] CombinedFees { get; set; }
    }

    public class SearchClauseOptions
    {
        public SearchClause PrivateOutdoorSpace { get; set; }
        public SearchClause AttendedLobby { get; set; }
        public SearchClause Views { get; set; }
    }

    public class MonthlyExpenses
    {
        public double DownPayment { get; set; }
        public double InterestRate { get; set; }
        public double TermInYears { get; set; }
        public bool IsAmount { get; set; }
        public bool IncludeMortgagePayment { get; set; }
        public double Min { get; set; }
        public double Max { get; set; }
    }
}
