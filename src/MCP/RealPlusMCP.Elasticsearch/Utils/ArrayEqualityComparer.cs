﻿namespace RealPlusNLP.Elasticsearch.Utils;

public class ArrayEqualityComparer<T> : IEqualityComparer<IEnumerable<T>>
{
    public static IEqualityComparer<IEnumerable<T>> Default { get; }
        = new ArrayEqualityComparer<T>();

    public bool Equals(IEnumerable<T> x, IEnumerable<T> y)
    {
        return x.SequenceEqual(y);
    }

    public int GetHashCode(IEnumerable<T> items)
    {
        if (items is null)
        {
            return 0;
        }

        var hashCode = 1341054025;
        foreach (var item in items)
        {
            hashCode = (hashCode * -1521134295) + EqualityComparer<T>.Default.GetHashCode(item);
        }

        return hashCode;
    }
}
