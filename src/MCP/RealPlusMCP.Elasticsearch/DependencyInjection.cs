﻿using System.Diagnostics.CodeAnalysis;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using RealPlusNLP.Elasticsearch.Contracts;
using RealPlusNLP.Elasticsearch.Creators.Listings;
using RealPlusNLP.Elasticsearch.Creators;
using RealPlusNLP.Elasticsearch.Documents;
using RealPlusNLP.Elasticsearch.Options;
using RealPlusNLP.Elasticsearch.Services;

namespace RealPlusNLP.Elasticsearch;

[ExcludeFromCodeCoverage]
public static class DependencyInjection
{
    public static IServiceCollection AddElasticsearchServices(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddOptions<ElasticsearchSettings>()
            .Bind(configuration.GetSection(ElasticsearchSettings.OptionKey))
        .ValidateDataAnnotations()
        .ValidateOnStart();

        services.AddKeyedSingleton<IElasticSearchQueryUpdater<ListingDocument>, ElasticSearchCommonQueryUpdater>(nameof(ElasticSearchCommonQueryUpdater));
        services.AddKeyedSingleton<IElasticSearchQueryUpdater<ListingDocument>, ElasticSearchListingQueryUpdater>(nameof(ElasticSearchListingQueryUpdater));
        services.AddKeyedSingleton<IElasticSearchQueryUpdater<ListingDocument>, ElasticSearchUnitQueryUpdater>(nameof(ElasticSearchUnitQueryUpdater));
        services.AddKeyedSingleton<IElasticSearchQueryUpdater<ListingDocument>, ElasticSearchBuildingQueryUpdater>(nameof(ElasticSearchBuildingQueryUpdater));
        services.AddSingleton<IReadOnlyCollection<IElasticSearchQueryUpdater<ListingDocument>>>(s =>
        {
            return
            [
                s.GetKeyedService<IElasticSearchQueryUpdater<ListingDocument>>(nameof(ElasticSearchCommonQueryUpdater))!,
                s.GetKeyedService<IElasticSearchQueryUpdater<ListingDocument>>(nameof(ElasticSearchListingQueryUpdater))!,
                s.GetKeyedService<IElasticSearchQueryUpdater<ListingDocument>>(nameof(ElasticSearchUnitQueryUpdater))!,
                s.GetKeyedService<IElasticSearchQueryUpdater<ListingDocument>>(nameof(ElasticSearchBuildingQueryUpdater))!
            ];
        });
        services.AddSingleton<IElasticSearchQueryCreator<ListingDocument>, ElasticSearchListingQueryCreator>();
        services.AddSingleton<IElasticSearchService, ElasticSearchService>();

        return services;
    }
}
