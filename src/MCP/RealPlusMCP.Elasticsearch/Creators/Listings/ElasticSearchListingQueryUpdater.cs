﻿using System.Linq.Expressions;
using Nest;
using RealPlusNLP.Elasticsearch.Contracts;
using RealPlusNLP.Elasticsearch.Documents;
using RealPlusNLP.Elasticsearch.Messages;

namespace RealPlusNLP.Elasticsearch.Creators.Listings;

internal sealed class ElasticSearchListingQueryUpdater : IElasticSearchQueryUpdater<ListingDocument>
{
    private const int SalesListedUpdatedActivityId = 1552;
    private const int SalesListedActivityId = 1553;
    private const int SalesUpdatedActivityId = 1554;
    private const int SalesContractSignedActivityId = 3697;
    private const int RentalsListedUpdatedActivityId = 3701;
    private const int RentalsListedActivityId = 3702;
    private const int RentalsUpdatedActivityId = 3703;
    private const int RentalsLeaseSignedActivityId = 3699;
    private const int ExclusiveListingTypeAttributeId = 14;
    private const int PrivateCobrokeListingTypeAttributeId = 3847;
    private const string MonthToMonth = "Month to Month";
    private static readonly IReadOnlyCollection<string> MonthToMonths = new List<string> { "Month2Month", "Month-Month", "Month to Mon" };
    private static readonly IReadOnlyCollection<int> OffMarketListingStatuses = new List<int> { 159, 162 };
    private static readonly IReadOnlyCollection<int> SoldListingStatuses = new List<int> { 156, 3598 };
    private static readonly IReadOnlyCollection<int> RentedListingStatuses = new List<int> { 220, 3602 };
    private static readonly IReadOnlyCollection<int> ContractLeaseSignedStatuses = new List<int> { 219, 3695, 3597, 157, 3690, 3597 };
    private static readonly IReadOnlyDictionary<int, int> RentalsListingTypesMapping = new Dictionary<int, int>
    {
        { 3524, 13 }, { 3523, 14 }, { 3528, 15 }, { 3525, 16 }, { 3527, 17 }, { 3526, 18 }, { 3876, 3876 }, { 3899, 3899 }
    };

    public void Update(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        AddIncludeOtherApartmentTypesCriteria(options, criteria);
        AddIncludeNominalSalesCriteria(options, criteria);
        AddListingCompaniesCriteria(options, criteria);
        AddPriceCriteria(options, criteria);
        AddMonthlyExpenseCriteria(options, criteria);
        AddPricePerSqFtCriteria(options, criteria);
        AddOpenHouseDateCriteria(options, criteria);
        AddOpenHouseTypeCriteria(options, criteria);
        AddAvailableDateCriteria(options, criteria);
        AddListingTypesCriteria(options, criteria);
        AddPriceChangeCriteria(options, criteria);
        AddOfficeIdsCriteria(options, criteria);
        AddFeePaysFreeRentConcessionsCriteria(options, criteria);
        AddLeaseTypeCriteria(options, criteria);
        AddAgentTagsCriteria(options, criteria);
        AddAgentIdsCriteria(options, criteria);
        AddMinLeaseTermCriteria(options, criteria);
        AddMaxLeaseTermCriteria(options, criteria);
        AddMediaAssetsCriteria(options, criteria);
        AddContactPhoneTagsCriteria(options, criteria);
        AddDealBuyerSellerAgentCriteria(options, criteria);
        AddCoBrokeAgreementCriteria(options, criteria);
        AddCollectYourOwnFeesCriteria(options, criteria);
        AddLeaseTermsCriteria(options, criteria);
        AddGuarantorsCriteria(options, criteria);
        AddKeyLocationCriteria(options, criteria);
        AddIncludeMortgagePayment(options, criteria);

        var customShouldCriterias = new List<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>>();
        AddStatusIdCriteria(options, customShouldCriterias);
        AddContractLeaseSignedActivityDateCriteria(options, customShouldCriterias);
        AddSoldActivityDateCriteria(options, customShouldCriterias);
        AddListedActivityDateCriteria(options, customShouldCriterias);
        criteria.Add(mo => mo.Bool(bo => bo.Should(customShouldCriterias)));
    }

    private void AddMediaAssetsCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        criteria.Add(m => m
            .Bool(b => b
                .Should(
                    s => !options.MediaAssets.HasPhotos.HasValue ? s : s.Term(t => t.Field(f => f.MediaAsset.HasPhoto).Value(options.MediaAssets.HasPhotos.Value)),
                    s => !options.MediaAssets.HasFloorplan.HasValue ? s : s.Term(t => t.Field(f => f.MediaAsset.HasFloorplan).Value(options.MediaAssets.HasFloorplan.Value)),
                    s => !options.MediaAssets.HasVideos.HasValue ? s : s.Term(t => t.Field(f => f.MediaAsset.HasVideo).Value(options.MediaAssets.HasVideos.Value)),
                    s => !options.MediaAssets.HasVirtualTours.HasValue ? s : s.Term(t => t.Field(f => f.MediaAsset.HasTour).Value(options.MediaAssets.HasVirtualTours.Value)))));
    }

    public void UpdateShouldClauses(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        AddListingNumberCriteria(options, criteria);
        AddWebIdCriteria(options, criteria);
    }

    public void UpdateMustClauses(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
    }

    private static void AddMinLeaseTermCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        if (options.MinLeaseTerm.HasValue && options.MinLeaseTerm.Value > 0)
        {
            Func<QueryContainerDescriptor<ListingDocument>, QueryContainer> minTermCriteria = m => m.Range(r => r.Field(f => f.MinLeaseTerm).GreaterThanOrEquals(options.MinLeaseTerm));
            if (options.MaxLeaseTerm.HasValue && options.MaxLeaseTerm.Value > 0)
            {
                minTermCriteria = m => m.Range(r => r.Field(f => f.MinLeaseTerm).GreaterThanOrEquals(options.MinLeaseTerm).LessThanOrEquals(options.MaxLeaseTerm));
            }
            criteria.Add(minTermCriteria);
        }
    }

    private static void AddMaxLeaseTermCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        if (options.MaxLeaseTerm.HasValue && options.MaxLeaseTerm.Value > 0)
        {
            Func<QueryContainerDescriptor<ListingDocument>, QueryContainer> maxTermCriteria = m => m.Range(r => r.Field(f => f.MaxLeaseTerm).LessThanOrEquals(options.MaxLeaseTerm));
            if (options.MinLeaseTerm.HasValue && options.MinLeaseTerm.Value > 0)
            {
                maxTermCriteria = m => m.Range(r => r.Field(f => f.MaxLeaseTerm).LessThanOrEquals(options.MaxLeaseTerm).GreaterThanOrEquals(options.MinLeaseTerm));
            }
            criteria.Add(maxTermCriteria);
        }
    }

    private static void AddIncludeOtherApartmentTypesCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        if (!options.IncludeOtherApartmentTypes && options.HasClosedStatus() && options.IsSales())
        {
            criteria.Add(m => m.Bool(b => b.MustNot(mn => mn.Terms(t => t.Field(f => f.AcrisPropertyType).Terms("MR", "PS", "SR", "TS")))));
        }
    }

    private static void AddIncludeNominalSalesCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        if (!options.IncludeNominalSales && options.HasClosedStatus() && options.IsSales())
        {
            criteria.Add(m => m.Bool(b => b.MustNot(mn => mn.Term(t => t.Field(f => f.NonMarketSale).Value("Y")))));
        }
    }

    private static void AddStatusIdCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        if (!(options.StatusIds?.Length > 0))
        {
            return;
        }

        var statusIds = options.StatusIds.ToList();

        if (IsSoldActivityDateProvided(options))
        {
            statusIds = statusIds.Except(SoldListingStatuses).Except(RentedListingStatuses).ToList();
        }

        if (!IsSoldActivityDateProvided(options) && IsContractLeaseSignedActivityDateProvided(options) && (statusIds.Any(SoldListingStatuses.Contains) || statusIds.Any(RentedListingStatuses.Contains)))
        {
            statusIds = statusIds.Except(SoldListingStatuses).Except(RentedListingStatuses).ToList();
        }

        // new contract signed dates or old saved criterias
        if (IsContractLeaseSignedActivityDateProvided(options) || IsListedUpdatedActivityDateProvided(options) && options.ListedUpdatedActivityId == SalesContractSignedActivityId || options.ListedUpdatedActivityId == RentalsLeaseSignedActivityId)
        {
            statusIds = statusIds.Except(ContractLeaseSignedStatuses).ToList();
        }

        if (IsListedUpdatedActivityDateProvided(options) && options.ListedUpdatedActivityId != SalesContractSignedActivityId && options.ListedUpdatedActivityId != RentalsLeaseSignedActivityId || !(statusIds.Count > 0))
        {
            return;
        }

        var verifiedOnly = (statusIds.Any(SoldListingStatuses.Contains) || statusIds.Any(RentedListingStatuses.Contains)) && options.VerifiedOnly;
        if (verifiedOnly && options.IncludeAlsoRentSaleListing)
        {
            var soldRentedStatuses = options.StatusIds.Where(SoldListingStatuses.Contains).Concat(options.StatusIds.Where(RentedListingStatuses.Contains)).ToList();
            var closedStatuses = options.StatusIds.Where(SoldListingStatuses.Contains)
                    .Concat(options.StatusIds.Where(RentedListingStatuses.Contains))
                    .Concat(options.StatusIds.Where(OffMarketListingStatuses.Contains)).ToList();
            var notClosedStatuses = statusIds.Except(closedStatuses).ToList();
            var offMarketStatuses = statusIds.Except(SoldListingStatuses).Except(RentedListingStatuses).Except(notClosedStatuses).ToList();
            criteria.Add(c => c.Bool(
                b => b.Should(
                    s => s.Bool(m => GetVerifiedOnlyQuery(m, soldRentedStatuses, options)),
                    s => s.Bool(m => GetIncludeAlsoRentSaleListingQuery(m, notClosedStatuses)),
                    n => offMarketStatuses.Any() ? n.Terms(t => t.Field(f => f.StatusId).Terms(offMarketStatuses)) : n)));
        }
        else if (verifiedOnly)
        {
            var notSoldRentedStatuses = statusIds.Except(SoldListingStatuses).Except(RentedListingStatuses).ToList();
            var soldRentedStatuses = options.StatusIds.Where(SoldListingStatuses.Contains).Concat(options.StatusIds.Where(RentedListingStatuses.Contains)).ToList();
            criteria.Add(c => c.Bool(
                b => b.Should(
                    s => s.Bool(m => GetVerifiedOnlyQuery(m, soldRentedStatuses, options)),
                    n => n.Terms(t => t.Field(f => f.StatusId).Terms(notSoldRentedStatuses)))));
        }
        else if (options.IncludeAlsoRentSaleListing)
        {
            var closedStatuses = options.StatusIds.Where(SoldListingStatuses.Contains)
                    .Concat(options.StatusIds.Where(RentedListingStatuses.Contains))
                    .Concat(options.StatusIds.Where(OffMarketListingStatuses.Contains)).ToList();
            var notClosedStatuses = statusIds.Except(closedStatuses).ToList();

            criteria.Add(c => c.Bool(
                    b => b.Should(
                                    s => s.Bool(m => GetIncludeAlsoRentSaleListingQuery(m, notClosedStatuses)),
                                    s => closedStatuses.Any() ? s.Terms(t => t.Field(f => f.StatusId).Terms(closedStatuses)) : s)));
        }
        else
        {
            criteria.Add(m => m.Terms(t => t.Field(f => f.StatusId).Terms(statusIds)));
        }
    }

    private static BoolQueryDescriptor<ListingDocument> GetIncludeAlsoRentSaleListingQuery(BoolQueryDescriptor<ListingDocument> m, List<int> notClosedStatuses)
    {
        return m.Must(
                n => n.Term(t => t.Field(f => f.HasRentSaleListing).Value(true)),
                n => n.Terms(t => t.Field(f => f.StatusId).Terms(notClosedStatuses)));
    }

    private static BoolQueryDescriptor<ListingDocument> GetVerifiedOnlyQuery(BoolQueryDescriptor<ListingDocument> m, List<int> soldRentedStatuses, ElasticSearchOptions options)
    {
        return m.Must(
                n => n.Term(t => t.Field(f => f.Verified).Value(true)),
                n => n.Terms(t => t.Field(f => f.StatusId).Terms(soldRentedStatuses)),
                n => GetDealsFinancedQuery(options, n));
    }

    private static QueryContainer GetDealsFinancedQuery(ElasticSearchOptions options, QueryContainerDescriptor<ListingDocument> n)
    {
        return n.Bool(b => b.Should(
                           sh => !options.DealsWithoutFinancingOnly
                                 ? sh
                                 : sh.Term(t => t.Field(f => f.ACRISAdditionals.DealFinanced).Value(false)),
                           sh => !options.FinancedDealsOnly
                                 ? sh
                                 : sh.Term(t => t.Field(f => f.ACRISAdditionals.DealFinanced).Value(true))));
    }

    private static void AddListingCompaniesCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        // listing company
        if (options.ListingCompanies?.Length > 0)
        {
            criteria.Add(m => m.Terms(t => t.Field(f => f.ListingCompanyName).Terms(options.ListingCompanies)));
        }
    }

    private static void AddPriceCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        // price
        if (!(options.PriceMin > 0) && !(options.PriceMax > 0))
        {
            return;
        }

        NumericRangeQueryDescriptor<ListingDocument> ClosingRangeQuery(NumericRangeQueryDescriptor<ListingDocument> rq)
        {
            rq.Field(f => f.ClosingPrice);
            if (options.PriceMin > 0)
            {
                rq.GreaterThanOrEquals(options.PriceMin);
            }

            if (options.PriceMax > 0)
            {
                rq.LessThanOrEquals(options.PriceMax);
            }

            return rq;
        }
        NumericRangeQueryDescriptor<ListingDocument> RangeQuery(NumericRangeQueryDescriptor<ListingDocument> rq)
        {
            rq.Field(f => f.Price);
            if (options.PriceMin > 0)
            {
                rq.GreaterThanOrEquals(options.PriceMin);
            }

            if (options.PriceMax > 0)
            {
                rq.LessThanOrEquals(options.PriceMax);
            }

            return rq;
        }
        criteria.Add(mo => mo.Bool(bo => bo.Should(new List<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>>
        {
            m => m.Bool(b => b.Must(new List<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>>
            {
                mi => mi.Term(t => t.Field(f => f.Closed).Value(true)),
                mi => mi.Range(ClosingRangeQuery)
            })),
            m => m.Bool(b => b.Must(new List<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>>
            {
                mi => mi.Term(t => t.Field(f => f.Closed).Value(false)),
                mi => mi.Range(RangeQuery)
            }))
        })));
    }

    private static void AddMonthlyExpenseCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        if (!(options.MonthlyExpensesOptions.Min > 0) && !(options.MonthlyExpensesOptions.Max > 0) || options.MonthlyExpensesOptions.IncludeMortgagePayment)
        {
            return;
        }

        NumericRangeQueryDescriptor<ListingDocument> RangeQuery(NumericRangeQueryDescriptor<ListingDocument> rq)
        {
            rq.Field(f => f.MonthlyExpence);
            if (options.MonthlyExpensesOptions.Min > 0)
            {
                rq.GreaterThanOrEquals(options.MonthlyExpensesOptions.Min);
            }

            if (options.MonthlyExpensesOptions.Max > 0)
            {
                rq.LessThanOrEquals(options.MonthlyExpensesOptions.Max);
            }

            return rq;
        }

        criteria.Add(m => m.Range(RangeQuery));
    }

    private static void AddPricePerSqFtCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        if (!(options.PricePerSqFtMin > 0) && !(options.PricePerSqFtMax > 0))
        {
            return;
        }

        NumericRangeQueryDescriptor<ListingDocument> RangeQuery(NumericRangeQueryDescriptor<ListingDocument> rq)
        {
            rq.Field(f => f.PricePerSqFt);
            if (options.PricePerSqFtMin > 0)
            {
                rq.GreaterThanOrEquals(options.PricePerSqFtMin);
            }

            if (options.PricePerSqFtMax > 0)
            {
                rq.LessThanOrEquals(options.PricePerSqFtMax);
            }

            return rq;
        }
        if (options.IncludeMissingSqFt)
        {
            criteria.Add(mo => mo.Bool(bo => bo.Should(new List<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>>
            {
                c => c.Range(RangeQuery),
                c => c.Bool(b => b.MustNot(m => m.Exists(e => e.Field(f => f.PricePerSqFt)))),
                c => c.Term(t => t.Field(f => f.PricePerSqFt).Value(0))
            })));
        }
        else
        {
            criteria.Add(mo => mo.Bool(bo => bo.Should(new List<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>>
            {
                m => m.Range(RangeQuery)
            })));
        }
    }

    private static void AddContractLeaseSignedActivityDateCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        if (options.StatusIds != null
            && (IsContractLeaseSignedActivityDateProvided(options) && (options.StatusIds.Any(ContractLeaseSignedStatuses.Contains) || options.StatusIds.Any(RentedListingStatuses.Contains))
            || IsListedUpdatedActivityDateProvided(options) && (options.ListedUpdatedActivityId == SalesContractSignedActivityId || options.ListedUpdatedActivityId == RentalsLeaseSignedActivityId)))
        {
            var contractLeaseSignedQueries = new List<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>>();

            var soldRentedStatuses = options.StatusIds.Where(statusId => SoldListingStatuses.Contains(statusId) || RentedListingStatuses.Contains(statusId)).ToList();
            var contractSignedStatuses = options.StatusIds.Where(ContractLeaseSignedStatuses.Contains).ToList();
            var targetDateStart = IsListedUpdatedActivityDateProvided(options) && options.ListedUpdatedActivityId == SalesContractSignedActivityId || options.ListedUpdatedActivityId == RentalsLeaseSignedActivityId
                    ? options.ListedUpdatedActivityDateStart.Value.ToString(Constants.ElasticSearchConstants.DateFormat)
                    : options.ContractLeaseSignedActivityDateStart.Value.ToString(Constants.ElasticSearchConstants.DateFormat);
            var targetDateEnd = IsListedUpdatedActivityDateProvided(options) && options.ListedUpdatedActivityId == SalesContractSignedActivityId || options.ListedUpdatedActivityId == RentalsLeaseSignedActivityId
                    ? options.ListedUpdatedActivityDateEnd.HasValue ? options.ListedUpdatedActivityDateEnd.Value.ToString(Constants.ElasticSearchConstants.DateFormat) : string.Empty
                    : options.ContractLeaseSignedActivityDateEnd.HasValue ? options.ContractLeaseSignedActivityDateEnd.Value.ToString(Constants.ElasticSearchConstants.DateFormat) : string.Empty;

            if (IsSoldRentedAnyDate(options))
            {
                if (options.VerifiedOnly && soldRentedStatuses.Any(SoldListingStatuses.Contains))
                {
                    contractLeaseSignedQueries.Add(c => c.Bool(
                        b => b.Must(
                            m => m.Term(t => t.Field(f => f.Verified).Value(true)),
                            m => m.Terms(t => t.Field(f => f.StatusId).Terms(soldRentedStatuses)),
                            m => GetDealsFinancedQuery(options, m),
                            m => GetDateRangeQuery(m, f => f.ContractSignDate, targetDateStart, targetDateEnd))));
                }
                else
                {
                    contractLeaseSignedQueries.Add(c => c.Bool(
                        b => b.Must(
                            m => m.Terms(t => t.Field(f => f.StatusId).Terms(soldRentedStatuses)),
                            m => GetDateRangeQuery(m, f => f.ContractSignDate, targetDateStart, targetDateEnd))));
                }
            }

            if (contractSignedStatuses.Any())
            {
                contractLeaseSignedQueries.Add(c => c.Bool(
                        b => b.Must(
                            m => m.Terms(t => t.Field(f => f.StatusId).Terms(contractSignedStatuses)),
                            m => GetDateRangeQuery(m, f => f.ContractSignDate, targetDateStart, targetDateEnd))));
            }

            criteria.Add(c => c.Bool(b => b.Should(contractLeaseSignedQueries)));
        }
    }

    private static void AddSoldActivityDateCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        if (options.StatusIds == null ||
            !options.StatusIds.Any(SoldListingStatuses.Contains) &&
             !options.StatusIds.Any(RentedListingStatuses.Contains))
        {
            return;
        }

        var soldRentedStatuses = options.StatusIds.Where(SoldListingStatuses.Contains).Concat(options.StatusIds.Where(RentedListingStatuses.Contains));
        if (IsContractLeaseSignedActivityDateProvided(options))
        {
            criteria.Add(c => c.Bool(
                b => b.Must(
                    m => m.Terms(t => t.Field(f => f.StatusId).Terms(soldRentedStatuses)),
                    m => options.VerifiedOnly ? m.Term(t => t.Field(f => f.Verified).Value(true)) : m,
                    m => GetDealsFinancedQuery(options, m),
                    m => GetDateRangeQuery(m, f => f.ContractSignDate, options.ContractLeaseSignedActivityDateStart, options.ContractLeaseSignedActivityDateEnd))));
        }

        if (IsSoldActivityDateProvided(options))
        {
            if (options.VerifiedOnly && options.HasClosedStatus())
            {
                criteria.Add(c => c.Bool(
                    b => b.Must(
                        m => m.Term(t => t.Field(f => f.Verified).Value(true)),
                        m => m.Terms(t => t.Field(f => f.StatusId).Terms(soldRentedStatuses)),
                        m => GetDealsFinancedQuery(options, m),
                        m => GetDateRangeQuery(m, f => f.SoldActivityDate, options.SoldActivityDateStart, options.SoldActivityDateEnd))));
            }
            else
            {
                criteria.Add(c => c.Bool(
                    b => b.Must(
                        m => m.Terms(t => t.Field(f => f.StatusId).Terms(soldRentedStatuses)),
                        m => GetDateRangeQuery(m, f => f.SoldActivityDate, options.SoldActivityDateStart, options.SoldActivityDateEnd))));
            }
        }
    }

    private static void AddListedActivityDateCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        if (!IsListedUpdatedActivityDateProvided(options))
        {
            return;
        }

        var listedUpdateQuery = GetListedUpdatedQueryPart(options);

        criteria.Add(listedUpdateQuery);
    }

    private static void AddOpenHouseDateCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        if (!options.OpenHouseDateStart.HasValue)
        {
            return;
        }

        criteria.Add(mo => mo.Bool(bo => bo.Should(new List<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>>
        {
            m => GetDateRangeQuery(m, f => f.OpenHouses.First().StartDate, options.OpenHouseDateStart, options.OpenHouseDateEnd)
        })));
    }

    private static void AddOpenHouseTypeCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        if (options.OpenHouseType == OpenHouseType.None)
        {
            return;
        }

        IBoolQuery TermQuery(BoolQueryDescriptor<ListingDocument> boolQueryDescriptor)
        {
            bool Has(OpenHouseType openHouseType, OpenHouseType value) => (openHouseType & value) > 0;

            IReadOnlyCollection<OpenHouseType> GetSubtypes(OpenHouseType openHouseType, OpenHouseType[] subTypes)
            {
                return subTypes.Where(subType => Has(openHouseType, subType)).ToList();
            }

            if (Has(options.OpenHouseType, OpenHouseType.Any))
            {
                boolQueryDescriptor.Must(m => m.Exists(t => t.Field(f => f.OpenHouses)));
                return boolQueryDescriptor;
            }

            var terms = new List<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>>();
            var ohTypes = new List<OpenHouseType>();

            void AddSubTypes(OpenHouseType type, IReadOnlyCollection<OpenHouseType> subTypes)
            {
                ohTypes.Add(type | OpenHouseType.ByAppointment);
                ohTypes.Add(type | OpenHouseType.Virtual);
                ohTypes.Add(type | OpenHouseType.ByAppointment | OpenHouseType.Virtual);
                if (subTypes.Count > 0)
                {
                    ohTypes.Remove(type);
                    if (subTypes.Contains(OpenHouseType.ByAppointment) && subTypes.Contains(OpenHouseType.Virtual))
                    {
                        ohTypes.Remove(type | OpenHouseType.ByAppointment);
                        ohTypes.Remove(type | OpenHouseType.Virtual);
                    }
                    else if (subTypes.Contains(OpenHouseType.ByAppointment))
                    {
                        ohTypes.Remove(type | OpenHouseType.Virtual);
                        ohTypes.Remove(type | OpenHouseType.ByAppointment | OpenHouseType.Virtual);
                    }
                    else if (subTypes.Contains(OpenHouseType.Virtual))
                    {
                        ohTypes.Remove(type | OpenHouseType.ByAppointment);
                        ohTypes.Remove(type | OpenHouseType.ByAppointment | OpenHouseType.Virtual);
                    }
                }
            }

            var ohSubTypes = GetSubtypes(options.OpenHouseType, new OpenHouseType[] { OpenHouseType.ByAppointment, OpenHouseType.Virtual });
            if (Has(options.OpenHouseType, OpenHouseType.Public))
            {
                ohTypes.Add(OpenHouseType.Public);
                AddSubTypes(OpenHouseType.Public, ohSubTypes);
            }

            if (Has(options.OpenHouseType, OpenHouseType.BrokerOnly))
            {
                ohTypes.Add(OpenHouseType.BrokerOnly);
                AddSubTypes(OpenHouseType.BrokerOnly, ohSubTypes);
            }

            terms.AddRange(ohTypes.Select(ohType => (Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>)(m => m.Term(t => t.Field(f => f.OpenHouses.First().OpenHouseType).Value(ohType)))));

            boolQueryDescriptor.Should(terms);
            return boolQueryDescriptor;
        }

        criteria.Add(c => c.Bool(TermQuery));
    }

    private static void AddAvailableDateCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        if (!options.AvailableDateStart.HasValue)
        {
            return;
        }

        criteria.Add(mo => mo.Bool(bo => bo.Should(new List<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>>
        {
            m => GetDateRangeQuery(m, f => f.AvailableDate, options.AvailableDateStart, options.AvailableDateEnd)
        })));
    }

    private static void AddListingTypesCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        if (!(options.ListingTypes?.Length > 0))
        {
            return;
        }
        var listingTypes = options.ListingTypes.ToList();

        if (options.IsSales())
        {
            if (listingTypes.Contains(ExclusiveListingTypeAttributeId))
            {
                listingTypes.Add(PrivateCobrokeListingTypeAttributeId);
            }
            criteria.Add(m => m.Terms(t => t.Field(f => f.ListingType).Terms(listingTypes)));
        }
        else
        {
            var rentalsListingTypes = RentalsListingTypesMapping
                .Where(rentalsListingTypeMap => listingTypes.Contains(rentalsListingTypeMap.Key))
                .Select(rentalsListingTypeMap => rentalsListingTypeMap.Value)
                .ToList();
            if (rentalsListingTypes.Contains(ExclusiveListingTypeAttributeId))
            {
                rentalsListingTypes.Add(PrivateCobrokeListingTypeAttributeId);
            }
            criteria.Add(m => m.Terms(t => t.Field(f => f.ListingType).Terms(rentalsListingTypes)));
        }
    }

    private static void AddPriceChangeCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        if (string.IsNullOrEmpty(options.PriceChangeDirection))
        {
            return;
        }

        var priceChangeCriterias = new List<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>>
        {
            m => m.Term(t => t.Field(f => f.ListingPriceChange.ChangeDirection).Value(options.PriceChangeDirection))
        };

        if (options.PercentChangeMin > 0 || options.PercentChangeMax > 0)
        {
            NumericRangeQueryDescriptor<ListingDocument> RangeQuery(NumericRangeQueryDescriptor<ListingDocument> rq)
            {
                rq.Field(f => f.ListingPriceChange.ChangePercent);
                if (options.PercentChangeMin > 0)
                {
                    rq.GreaterThanOrEquals(options.PercentChangeMin);
                }

                if (options.PercentChangeMax > 0)
                {
                    rq.LessThanOrEquals(options.PercentChangeMax);
                }

                return rq;
            }
            priceChangeCriterias.Add(m => m.Range(RangeQuery));
        }

        if (options.PriceChangeStartDate.HasValue)
        {
            priceChangeCriterias.Add(m => GetDateRangeQuery(m, f => f.ListingPriceChange.ChangeDate, options.PriceChangeStartDate, options.PriceChangeEndDate));
        }

        criteria.Add(mo => mo.Bool(bo => bo.Must(priceChangeCriterias)));
    }

    private static void AddOfficeIdsCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        if (options.OfficeIds?.Length > 0)
        {
            criteria.Add(m => m.Terms(t => t.Field(f => f.Contact.OfficeIds).Terms(options.OfficeIds)));
        }
    }
    private static void AddCollectYourOwnFeesCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        if (options.CollectYourOwnFees?.Length > 0)
        {
            criteria.Add(m => m.Terms(t => t.Field(f => f.CollectYourOwnFee).Terms(options.CollectYourOwnFees)));
        }
    }

    private static void AddFeePaysFreeRentConcessionsCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        var combinedFeesExists = options.AdvancedFees?.CombinedFees?.Any() ?? false;
        var aggregatedFeesExists = options.AdvancedFees?.AggregatedFees?.Any() ?? false;

        Func<QueryContainerDescriptor<ListingDocument>, QueryContainer> FeeQuery(int[] terms)
        {
            return m => m.Terms(t => t.Field(f => f.FeePaidBy).Terms(terms));
        }

        Func<QueryContainerDescriptor<ListingDocument>, QueryContainer> FreeRentQuery(bool value)
        {
            return m => m.Term(t => t.Field(f => f.FreeRent.IsOffered).Value(value));
        }

        Func<QueryContainerDescriptor<ListingDocument>, QueryContainer> combinedFeesQuery = c => c.Bool(b => b.Must(
                                    FeeQuery(options.AdvancedFees.CombinedFees),
                                    FreeRentQuery(true)));

        Func<QueryContainerDescriptor<ListingDocument>, QueryContainer> aggregatedFeesQuery = c => c.Bool(b => b.Should(
                                            FeeQuery(options.AdvancedFees.AggregatedFees),
                                            FreeRentQuery(options.FreeRent.Value)));

        if (combinedFeesExists && !aggregatedFeesExists)
        {
            criteria.Add(combinedFeesQuery);
        }
        else if (combinedFeesExists && aggregatedFeesExists)
        {
            criteria.Add(c => c.Bool(b => b.Should(combinedFeesQuery, FeeQuery(options.AdvancedFees.AggregatedFees))));
        }
        else if (aggregatedFeesExists)
        {
            if (options.FreeRent.HasValue)
            {
                criteria.Add(aggregatedFeesQuery);
            }
            else
            {
                criteria.Add(FeeQuery(options.AdvancedFees.AggregatedFees));
            }
        }
        else if (options.FreeRent.HasValue)
        {
            criteria.Add(FreeRentQuery(options.FreeRent.Value));
        }
    }

    private static void AddLeaseTypeCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        if (options.LeaseTypes?.Length > 0)
        {
            criteria.Add(m => m.Terms(t => t.Field(f => f.LeaseType).Terms(options.LeaseTypes)));
        }
    }

    private static void AddCoBrokeAgreementCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        if (!string.IsNullOrEmpty(options.CoBrokeAgreement))
        {
            criteria.Add(c => c.Term(t => t.Field(f => f.CoBrokeAgreement).Value(options.CoBrokeAgreement)));
        }
    }

    private static void AddKeyLocationCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        if (options.KeyLocations?.Length > 0)
        {
            criteria.Add(m => m.Terms(t => t.Field(f => f.KeyLocationOffices).Terms(options.KeyLocations)));
        }
    }

    private static void AddLeaseTermsCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        if (options.LeaseTerms?.Length > 0)
        {
            var values = options.LeaseTerms.Contains(MonthToMonth) ? options.LeaseTerms.Concat(MonthToMonths).ToArray() : options.LeaseTerms;
            criteria.Add(m => m.Terms(t => t.Field(f => f.LeaseTerm).Terms(values)));
        }
    }

    private static void AddGuarantorsCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        if (options.Guarantors?.Length > 0)
        {
            criteria.Add(m => m.Terms(t => t.Field(f => f.GuarantorsAllowed).Terms(options.Guarantors)));
        }
    }

    private static void AddAgentTagsCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        if (options.AgentTags == null || options.AgentTags.Length <= 0)
        {
            return;
        }

        IQueryStringQuery CommonTermsQuery(QueryStringQueryDescriptor<ListingDocument> queryStringQueryDescriptor)
        {
            queryStringQueryDescriptor.Fields(f => f.Field(c => c.Contact.Names));
            queryStringQueryDescriptor.Query(string.Join(" ", options.AgentTags.Select(tag => $"\"{tag}\"")));
            return queryStringQueryDescriptor;
        }

        // criteria.Add(c => c.Bool(b => b.Should(s => s.QueryString(CommonTermsQuery))));
        criteria.Add(c => c.QueryString(CommonTermsQuery));
    }

    private static void AddContactPhoneTagsCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        if (options.ContactPhoneTags == null || options.ContactPhoneTags.Length == 0)
        {
            return;
        }

        criteria.Add(c => c.Terms(t => t.Field(f => f.Contact.Phones).Terms(options.ContactPhoneTags)));
    }

    private static void AddAgentIdsCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        if (options.AgentIds?.Length > 0)
        {
            criteria.Add(m => m.Terms(t => t.Field(f => f.Contact.AgentIds).Terms(options.AgentIds)));
        }
    }

    private static void AddListingNumberCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        if (options.ListingNumbers?.Length > 0)
        {
            criteria.Add(m => m.Terms(t => t.Field(f => f.ListingNumber).Terms(options.ListingNumbers)));
        }
    }

    private static void AddWebIdCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        if (options.WebIds?.Length > 0)
        {
            criteria.Add(m => m.Terms(t => t.Field(f => f.WebId).Terms(options.WebIds)));
        }
    }

    private static void AddDealBuyerSellerAgentCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        var checkedBuyer = options.Deals.BuyerAgent.HasValue && options.Deals.BuyerAgent.Value;
        var checkedSeller = options.Deals.SellerAgent.HasValue && options.Deals.SellerAgent.Value;
        if (!checkedBuyer && !checkedSeller || options.StatusIds == null || !options.StatusIds.Any(SoldListingStatuses.Contains) && !options.StatusIds.Any(RentedListingStatuses.Contains))
        {
            return;
        }

        criteria.Add(c => c.Bool(b => b.Must(
            m => m.Term(t => t.Field(f => f.Closed).Value(true)),
            m => m.Bool(
                bb => bb.Should(
                    s => checkedBuyer ? s.Terms(t => t.Field(f => f.Contact.DealContactSecureGroups).Terms(options.SecureGroup)) : s,
                    s => checkedSeller && options.Deals.SellerAgent.Value ? s.Term(t => t.SecureGroup, options.SecureGroup) : s)))));
    }

    private static void AddIncludeMortgagePayment(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        if (!options.MonthlyExpensesOptions.IncludeMortgagePayment)
        {
            return;
        }

        string script = "double monthlyIntRate = params.interestRate / 100.0 / 12.0;" +
            "double numPayments = params.numOfYears * 12;" +
            "double salePrice = !doc['price'].empty ? doc['price'].value : 0.0;" +
            "double paymentIntRate = Math.pow((1 + monthlyIntRate), numPayments);" +
            "double downPayment = params.isAmount ? params.downPayment : (salePrice * params.downPayment / 100);" +
            "downPayment = Math.min(downPayment, salePrice);" +
            "double actualAmtBorrowed = salePrice - downPayment;" +
            "double MonthlyAmount = actualAmtBorrowed > 1000000 ? (1000000 * monthlyIntRate) * paymentIntRate : (actualAmtBorrowed * monthlyIntRate) * paymentIntRate;" +
            "double monthlyPayment = paymentIntRate - 1;" +
            "monthlyPayment = actualAmtBorrowed * monthlyIntRate * paymentIntRate / monthlyPayment;" +
            "double monthlyPaymentWithTaxes = monthlyPayment + (!doc['monthlyExpence'].empty ? doc['monthlyExpence'].value : 0.0);" +
            "double minPayment = params.containsKey('minMonthlyExpence') ? params.minMonthlyExpence : Double.MIN_VALUE;" +
            "double maxPayment = !params.containsKey('maxMonthlyExpence') || params.maxMonthlyExpence == 0 ? Double.MAX_VALUE : params.maxMonthlyExpence;" +
            "return monthlyPaymentWithTaxes >= minPayment && monthlyPaymentWithTaxes <= maxPayment;";

        criteria.Add(c => c.Bool(b => b.Must(
            m => m.Script(
                s => s.Script(ss => ss
                    .Source(script)
                    .Lang("painless")
                    .Params(p => p
                        .Add("interestRate", options.MonthlyExpensesOptions.InterestRate)
                        .Add("numOfYears", options.MonthlyExpensesOptions.TermInYears)
                        .Add("downPayment", options.MonthlyExpensesOptions.DownPayment)
                        .Add("isAmount", options.MonthlyExpensesOptions.IsAmount)
                        .Add("minMonthlyExpence", options.MonthlyExpensesOptions.Min)
                        .Add("maxMonthlyExpence", options.MonthlyExpensesOptions.Max)
                        )
                    ))
            )));
    }

    private static Func<QueryContainerDescriptor<ListingDocument>, QueryContainer> GetListedUpdatedQueryPart(ElasticSearchOptions options)
    {
        var listedUpdatedSupportedStatusIds = GetListedUpdatedQuerySupportedStatusIds(options);
        if (listedUpdatedSupportedStatusIds?.Count == 0 && (IsSoldActivityDateProvided(options) || IsContractLeaseSignedActivityDateProvided(options)))
        {
            return null;
        }

        switch (options.ListedUpdatedActivityId)
        {
            case SalesListedActivityId:
            case RentalsListedActivityId:
                if (listedUpdatedSupportedStatusIds?.Count > 0)
                {
                    return mo => mo.Bool(bo =>
                        bo.Must(
                            m => m.Terms(t => t.Field(f => f.StatusId).Terms(listedUpdatedSupportedStatusIds)),
                            m => GetDateRangeQuery(m, f => f.ListedActivityDate, options.ListedUpdatedActivityDateStart, options.ListedUpdatedActivityDateEnd)));
                }
                else
                {
                    return m => GetDateRangeQuery(m, f => f.ListedActivityDate, options.ListedUpdatedActivityDateStart, options.ListedUpdatedActivityDateEnd);
                }
            case SalesUpdatedActivityId:
            case RentalsUpdatedActivityId:
                if (listedUpdatedSupportedStatusIds?.Count > 0)
                {
                    return mo => mo.Bool(bo =>
                        bo.Must(
                            m => m.Terms(t => t.Field(f => f.StatusId).Terms(listedUpdatedSupportedStatusIds)),
                            m => GetDateRangeQuery(m, f => f.UpdateDate, options.ListedUpdatedActivityDateStart, options.ListedUpdatedActivityDateEnd)));
                }
                else
                {
                    return m => GetDateRangeQuery(m, f => f.UpdateDate, options.ListedUpdatedActivityDateStart, options.ListedUpdatedActivityDateEnd);
                }
            case SalesListedUpdatedActivityId:
            case RentalsListedUpdatedActivityId:
            default:
                if (listedUpdatedSupportedStatusIds?.Count > 0)
                {
                    return mo => mo.Bool(bo =>
                        bo.Must(
                            m => m.Terms(t => t.Field(f => f.StatusId).Terms(listedUpdatedSupportedStatusIds)),
                            m => m.Bool(b => b.Should(
                                s => GetDateRangeQuery(s, f => f.ListedActivityDate, options.ListedUpdatedActivityDateStart, options.ListedUpdatedActivityDateEnd),
                                s => GetDateRangeQuery(s, f => f.UpdateDate, options.ListedUpdatedActivityDateStart, options.ListedUpdatedActivityDateEnd)))));
                }
                else
                {
                    return mo => mo.Bool(bo =>
                        bo.Should(
                            m => GetDateRangeQuery(m, f => f.ListedActivityDate, options.ListedUpdatedActivityDateStart, options.ListedUpdatedActivityDateEnd),
                            m => GetDateRangeQuery(m, f => f.UpdateDate, options.ListedUpdatedActivityDateStart, options.ListedUpdatedActivityDateEnd)));
                }
        }
    }

    private static IReadOnlyCollection<int> GetListedUpdatedQuerySupportedStatusIds(ElasticSearchOptions options)
    {
        var statusIds = options.StatusIds?.ToList();

        if (IsSoldRentedAnyDate(options) && IsContractLeaseSignedActivityDateProvided(options))
        {
            return statusIds?.Except(SoldListingStatuses).Except(RentedListingStatuses).Except(ContractLeaseSignedStatuses).ToList();
        }

        if (statusIds?.Count > 0 && IsSoldActivityDateProvided(options))
        {
            statusIds = statusIds.Except(SoldListingStatuses).Except(RentedListingStatuses).ToList();
        }

        if (statusIds?.Count > 0 && IsContractLeaseSignedActivityDateProvided(options))
        {
            statusIds = statusIds.Except(ContractLeaseSignedStatuses).ToList();
        }

        return statusIds;
    }

    private static bool IsSoldActivityDateProvided(ElasticSearchOptions options)
    {
        return options.SoldActivityDateStart.HasValue;
    }

    private static bool IsContractLeaseSignedActivityDateProvided(ElasticSearchOptions options)
    {
        return options.ContractLeaseSignedActivityDateStart.HasValue;
    }

    private static bool IsListedUpdatedActivityDateProvided(ElasticSearchOptions options)
    {
        return options.ListedUpdatedActivityDateStart.HasValue;
    }

    private static bool IsSoldRentedAnyDate(ElasticSearchOptions options)
    {
        return !IsSoldActivityDateProvided(options) && options.StatusIds != null && (options.StatusIds.Any(SoldListingStatuses.Contains) || options.StatusIds.Any(RentedListingStatuses.Contains));
    }

    private static QueryContainer GetDateRangeQuery(QueryContainerDescriptor<ListingDocument> descriptor, Expression<Func<ListingDocument, DateTime?>> filedPath, DateTime? startDate, DateTime? endDate)
    {
        return endDate.HasValue
            ? descriptor.DateRange(rq => rq.Field(filedPath)
                        .GreaterThanOrEquals(startDate.Value.ToString(Constants.ElasticSearchConstants.DateFormat))
                        .LessThanOrEquals(endDate.Value.ToString(Constants.ElasticSearchConstants.DateFormat))
                        .Format(Constants.ElasticSearchConstants.DateFormat))
            : descriptor.DateRange(rq => rq.Field(filedPath)
                        .GreaterThanOrEquals(startDate.Value.ToString(Constants.ElasticSearchConstants.DateFormat))
                        .Format(Constants.ElasticSearchConstants.DateFormat));
    }

    private static QueryContainer GetDateRangeQuery(QueryContainerDescriptor<ListingDocument> descriptor, Expression<Func<ListingDocument, DateTime?>> filedPath, string startDate, string endDate)
    {
        return !string.IsNullOrEmpty(endDate)
            ? descriptor.DateRange(rq => rq.Field(filedPath)
                        .GreaterThanOrEquals(startDate)
                        .LessThanOrEquals(endDate)
                        .Format(Constants.ElasticSearchConstants.DateFormat))
            : descriptor.DateRange(rq => rq.Field(filedPath)
                        .GreaterThanOrEquals(startDate)
                        .Format(Constants.ElasticSearchConstants.DateFormat));
    }
}