﻿using Nest;
using RealPlusNLP.Elasticsearch.Contracts;
using RealPlusNLP.Elasticsearch.Documents;
using RealPlusNLP.Elasticsearch.Messages;

namespace RealPlusNLP.Elasticsearch.Creators.Listings;

internal sealed class ElasticSearchCommonQueryUpdater : IElasticSearchQueryUpdater<ListingDocument>
{
    private const int WithFireplaceMiscellaneousEssentials = 3845;
    private const int LoftMiscellaneousEssentials = 3614;
    private const int PenthouseMiscellaneousEssentials = 3618;
    private const int ResaleApartmentMiscellaneousEssentials = 3900;

    private const int LaundryFacilitiesValue = 194;
    private const int WasherDryerValue = 2570;

    public void Update(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        AddMiscellaneousEssentialsCriteria(options, criteria);
        AddKeywordCriteria(options, criteria);
        AddLaundryFacilityCriteria(options, criteria);
    }

    public void UpdateShouldClauses(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
    }

    public void UpdateMustClauses(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
    }

    private static void AddMiscellaneousEssentialsCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        var miscellaneous = new List<int>();
        if (options.MiscellaneousEssentials != null)
        {
            miscellaneous.AddRange(options.MiscellaneousEssentials);
        }
        if (options.MiscellaneousDetails != null)
        {
            miscellaneous.AddRange(options.MiscellaneousDetails);
        }

        var essentials = miscellaneous
            .Where(essential => essential == LoftMiscellaneousEssentials || essential == PenthouseMiscellaneousEssentials || essential == WithFireplaceMiscellaneousEssentials || essential == ResaleApartmentMiscellaneousEssentials)
            .ToArray();

        if (essentials.Length == 0)
        {
            return;
        }

        IBoolQuery TermQuery(BoolQueryDescriptor<ListingDocument> boolQueryDescriptor)
        {
            var terms = new List<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>>();
            if (essentials.Any(essential => essential == WithFireplaceMiscellaneousEssentials))
            {
                // Replace with fireplace misc id with WithFireplace value check
                essentials = essentials.Where(essential => essential != WithFireplaceMiscellaneousEssentials).ToArray();
                terms.Add(c => c.Term(t => t.Field(f => f.Unit.WithFireplace).Value(true)));
            }
            if (essentials.Any(essential => essential == ResaleApartmentMiscellaneousEssentials))
            {
                // Replace with resale apartment misc id with ResaleApartment value check
                essentials = essentials.Where(essential => essential != ResaleApartmentMiscellaneousEssentials).ToArray();
                terms.Add(c => c.Term(t => t.Field(f => f.ResaleApartment).Value('R')));
            }
            if (essentials.Any())
            {
                terms.AddRange(essentials.Select(essential => (Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>)(m => m.Term(t => t.Field(f => f.Unit.MiscEssentials).Value(essential)))).ToList());
            }
            boolQueryDescriptor.Must(terms);
            return boolQueryDescriptor;
        }

        criteria.Add(c => c.Bool(TermQuery));
    }

    private static void AddKeywordCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        if (string.IsNullOrEmpty(options.Keyword))
        {
            return;
        }

        IMultiMatchQuery MultiMatchQuery(MultiMatchQueryDescriptor<ListingDocument> multiMatchQueryDescriptor, string keyword)
        {
            multiMatchQueryDescriptor.Fields(f => f
                .Field(c => c.Notes)
                .Field(c => c.Comments)
                .Field(c => c.Property.Notes)
                .Field(c => c.Property.Comments));
            multiMatchQueryDescriptor.Query(keyword);
            multiMatchQueryDescriptor.Type(TextQueryType.Phrase);
            multiMatchQueryDescriptor.MinimumShouldMatch(MinimumShouldMatch.Percentage(100));
            return multiMatchQueryDescriptor;
        }

        var should = new List<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>>();
        foreach (var keyword in options.Keyword.Split(new string[] { "||" }, StringSplitOptions.None))
        {
            should.Add(s => s.MultiMatch(mm => MultiMatchQuery(mm, keyword.Trim())));
        }

        criteria.Add(c => c.Bool(b => b.Should(should)));
    }

    private static void AddLaundryFacilityCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        if (options.MiscellaneousDetails != null && options.Amenities != null && Array.IndexOf(options.MiscellaneousDetails, WasherDryerValue) > -1 && Array.IndexOf(options.Amenities, LaundryFacilitiesValue) > -1)
        {
            IBoolQuery TermQuery(BoolQueryDescriptor<ListingDocument> boolQueryDescriptor)
            {
                boolQueryDescriptor.Should(
                    should => should.Term(t => t.Field(f => f.Unit.MiscDeatils).Value(WasherDryerValue)),
                    should => should.Term(t => t.Field(f => f.Property.BuildingAmenity).Value(LaundryFacilitiesValue)));

                return boolQueryDescriptor;
            }

            criteria.Add(c => c.Bool(TermQuery));
        }
    }
}