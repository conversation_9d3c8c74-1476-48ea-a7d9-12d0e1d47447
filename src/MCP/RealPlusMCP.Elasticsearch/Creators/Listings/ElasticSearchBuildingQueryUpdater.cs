﻿using Nest;
using RealPlusNLP.Elasticsearch.Contracts;
using RealPlusNLP.Elasticsearch.Documents;
using RealPlusNLP.Elasticsearch.Extensions;
using RealPlusNLP.Elasticsearch.Messages;

namespace RealPlusNLP.Elasticsearch.Creators.Listings;

internal sealed class ElasticSearchBuildingQueryUpdater : IElasticSearchQueryUpdater<ListingDocument>
{
    private const int AttendedLobbyMiscellaneousEssentials = 3617;
    private const int AttendedLobbyAnyValue = 186;
    private const int UnattendedLobbyValue = 190;
    private const int Doorman = 188;
    private static readonly int[] AttendedLobbyValues = { 186, 188, 3842, 3843, 187, 189 };
    private static readonly int[] DoormanTypes = { 3842, 3843 };
    private const int LaundryFacilitiesValue = 194;
    private const int WasherDryerValue = 2570;
    private static readonly int[] PropertyTownhouseFeatures = { 2597, 2599, 2600, 2602, 2604, 2604 };
    private static readonly IReadOnlyDictionary<int, int> RentalsSalesBuildingAllowIdsMapping = new Dictionary<int, int> { { 3704, 173 }, { 3705, 174 }, { 3781, 175 }, { 3707, 176 }, { 3708, 177 }, { 3709, 178 }, { 3792, 3786 }, { 3793, 3787 }, { 3794, 3788 }, { 3820, 3818 } };
    private static readonly IReadOnlyDictionary<int, int> RentalsSalesBuildingNotAllowIdsMapping = new Dictionary<int, int> { { 3710, 179 }, { 3711, 180 }, { 3782, 181 }, { 3713, 182 }, { 3714, 183 }, { 3715, 184 }, { 3795, 3789 }, { 3796, 3790 }, { 3797, 3791 }, { 3821, 3819 } };

    public void Update(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        AddBuildingPeriodCriteria(options, criteria);
        AddWidthCriteria(options, criteria);
        AddUnitsCriteria(options, criteria);
        AddBuildingTypeCriteria(options, criteria);
        AddBuildingAllowedCriteria(options, criteria);
        AddBuildingNotAllowedCriteria(options, criteria);
        AddYearBuiltCriteria(options, criteria);
        AddManagedBuildingCriteria(options, criteria);
        AddAmenitiesCriteria(options, criteria);
        AddLandLeaseCriteria(options, criteria);
        AddAttendedLobbyCriteria(options, criteria);
        AddAllowedFinancingPercent(options, criteria);
        AddConstructionType(options, criteria);
        AddCommonOutdoorSpace(options, criteria);
        AddBuildingContacts(options, criteria);
        AddBuildingFloorsCriteria(options, criteria);
        AddMarketedByCompanies(options, criteria);
    }

    private void AddMarketedByCompanies(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> mainCriteria)
    {
        if (options.MarketedByCompanies?.Any() ?? false)
        {
            mainCriteria.Add(o => o.Bool(b => b.Should(sh => sh.Terms(t => t.Field(f => f.Property.MarketedBy).Terms(options.MarketedByCompanies)))));
        }
    }

    private void AddBuildingContacts(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> mainCriteria)
    {
        var criterias = new List<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>>();
        if (options.ManagementCompanies?.Any() ?? false)
        {
            //criterias.Add(o => o.Terms(t => t.Field(f => f.Property.ManagementCompany.Suffix("keyword")).Terms(options.ManagementCompanies)));
            criterias.Add(o => o.Bool(b => b.Should(
                s => s.Terms(t => t.Field(f => f.Property.ManagementCompany.Suffix("keyword")).Terms(options.ManagementCompanies)),
                s => s.Terms(t => t.Field(f => f.Property.AlternativeManagementCompanies).Terms(options.ManagementCompanies)))));
        }

        if (options.Owners?.Any() ?? false)
        {
            criterias.Add(o => o.Terms(t => t.Field(f => f.Property.Owner.Suffix("keyword")).Terms(options.Owners)));
        }

        if (options.Landlords?.Any() ?? false)
        {
            criterias.Add(o => o.Terms(t => t.Field(f => f.Property.Landlord.Suffix("keyword")).Terms(options.Landlords)));
        }

        if (criterias.Any())
        {
            mainCriteria.Add(o => o.Bool(b => b.Should(criterias)));
        }
    }

    private void AddCommonOutdoorSpace(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        if (options.CommonOutdoorSpaces is null)
        {
            return;
        }

        criteria.Add(o => o.Terms(t => t.Field(f => f.Property.CommonOutdoorSpace).Terms(options.CommonOutdoorSpaces)));
    }

    private void AddConstructionType(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        var constructionTypesMissing = !(options.ConstructionTypes?.Length > 0);
        if (constructionTypesMissing && !options.Resale.HasValue)
        {
            return;
        }

        var constructionTypes = options.ConstructionTypes;
        if (constructionTypes != null && options.ConstructionTypes?.Length > 0)
        {
            var ct = constructionTypes.ToList();
            ct.Add(3803);
            constructionTypes = ct.ToArray();
        }
        criteria.Add(o => o
            .Bool(b => b
                .Should(
                    should => options.Resale.HasValue ? should.Term(t => t.Field(f => f.Property.Resale).Value(options.Resale.Value)) : should,
                    should => constructionTypesMissing ? should :
                        should.Bool(bb => bb
                                .Must(
                                    m => m.Term(t => t.Field(f => f.Property.NewDevelopment).Value(true)),
                                    m => m.Terms(t => t.Field(f => f.Property.ConstructionType).Terms(constructionTypes)))))));
    }

    private void AddAllowedFinancingPercent(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        if (options.IsRental())
        {
            return;
        }

        if (options.MaxAllowedFinancingPercent.HasValue && options.MinAllowedFinancingPercent.HasValue)
        {
            if (options.MaxAllowedFinancingPercent.Value < options.MinAllowedFinancingPercent.Value)
            {
                throw new InvalidOperationException($"{nameof(options.MaxAllowedFinancingPercent)} should be greater than {nameof(options.MinAllowedFinancingPercent)}.");
            }

            criteria.Add(o => o
                .Bool(b => b
                    .Must(m => m
                        .Range(r => r
                            .Field(f => f.Property.FinanceAllowedPercent)
                            .GreaterThanOrEquals(options.MinAllowedFinancingPercent.Value)
                            .LessThanOrEquals(options.MaxAllowedFinancingPercent.Value)))));

            return;
        }

        if (options.MaxAllowedFinancingPercent.HasValue)
        {
            criteria.Add(o => o
               .Bool(b => b
                   .Must(m => m
                       .Range(r => r
                           .Field(f => f.Property.FinanceAllowedPercent)
                           .LessThanOrEquals(options.MaxAllowedFinancingPercent.Value)))));
        }

        if (options.MinAllowedFinancingPercent.HasValue)
        {
            criteria.Add(o => o
               .Bool(b => b
                   .Must(m => m
                       .Range(r => r
                           .Field(f => f.Property.FinanceAllowedPercent)
                            .GreaterThanOrEquals(options.MinAllowedFinancingPercent.Value)))));
        }
    }

    public void UpdateShouldClauses(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        var addressUnits = options.AddressUnits?.Where(x => x.Unit != null && x.RPBin == null).ToList();
        if (addressUnits != null && addressUnits.Any() && (options.NeighborhoodIds?.Length > 0 || options.MapShapes?.Length > 0 || options.StreetGrids?.Length > 0 || options.PostCodes?.Length > 0))
        {
            AddComplexShouldClauses(options, criteria);
        }
        else
        {
            AddNeighborhoodIdCriteria(options, criteria);
            AddAddressUnitsCriteria(options, criteria);
            AddMapShapesCriteria(options, criteria);
            AddStreetGridsCriteria(options, criteria);
            AddSchoolCriteria(options, criteria);
            AddPostalCodeCriteria(options, criteria);
        }
    }

    private void AddSchoolCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        criteria.Add(GetSchoolCriteria(options));
    }

    private void AddLineCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        criteria.Add(GetLineCriteria(options));
    }

    public void UpdateMustClauses(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        AddTownhouseFeaturesCriteria(options, criteria);
        AddLineCriteria(options, criteria);
    }

    private static void AddComplexShouldClauses(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        var addressBuildingCriteria = GetAddressOnlyCriteria(options);
        if (addressBuildingCriteria != null)
        {
            criteria.Add(addressBuildingCriteria);
        }
        var addressBuildingIdAndUnitNumberCriterias = GetAddressUnitCriteria(options);
        addressBuildingIdAndUnitNumberCriterias?.ForEach(criteria.Add);

        var unitRelatedCriterias = new List<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>>();

        QueryContainer GetUnitCriteria(QueryContainerDescriptor<ListingDocument> c) => c.Bool(b => b.Should(GetUnitOnlyCriteria(options)));

        var neighborhoodIdCriteria = GetNeighborhoodIdCriteria(options);
        if (neighborhoodIdCriteria != null)
        {
            unitRelatedCriterias.Add(c => c.Bool(b => b.Must(GetUnitCriteria, neighborhoodIdCriteria)));
        }

        var circleCriterias = GetCircleShapesCriteria(options);
        circleCriterias?.ForEach(circleCriteria => unitRelatedCriterias.Add(
            c => c.Bool(b => b.Must(GetUnitCriteria, circleCriteria))));

        var polygonCriterias = GetPolygonShapesCriteria(options);
        polygonCriterias?.ForEach(polygonCriteria => unitRelatedCriterias.Add(
            c => c.Bool(b => b.Must(GetUnitCriteria, polygonCriteria))));

        var streetGridCriterias = GetStreetGridsCriteria(options);
        streetGridCriterias?.ForEach(streetGridCriteria => unitRelatedCriterias.Add(
            c => c.Bool(b => b.Must(GetUnitCriteria, streetGridCriteria))));

        var postalCodeCriteria = GetPostalCodeCriteria(options);
        if (postalCodeCriteria != null)
        {
            unitRelatedCriterias.Add(c => c.Bool(b => b.Must(GetUnitCriteria, postalCodeCriteria)));
        }

        var schoolCriteria = GetSchoolCriteria(options);
        if (schoolCriteria != null)
        {
            unitRelatedCriterias.Add(c => c.Bool(b => b.Must(GetUnitCriteria, schoolCriteria)));
        }

        criteria.Add(c => c.Bool(b => b.Should(unitRelatedCriterias)));
    }

    private static void AddBuildingPeriodCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        // building period
        if (options.BuildingPeriods?.Length > 0)
        {
            criteria.Add(m => m.Terms(t => t.Field(f => f.Property.BuildingPeriod).Terms(options.BuildingPeriods)));
        }
    }

    private static void AddNeighborhoodIdCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        var neighborhoodIdCriteria = GetNeighborhoodIdCriteria(options);
        if (neighborhoodIdCriteria != null)
        {
            criteria.Add(neighborhoodIdCriteria);
        }
    }

    private static Func<QueryContainerDescriptor<ListingDocument>, QueryContainer> GetNeighborhoodIdCriteria(ElasticSearchOptions options)
    {
        return options.NeighborhoodIds?.Length > 0
            ? (m => m.Terms(t => t.Field(f => f.Property.NeighborhoodId).Terms(options.NeighborhoodIds)))
            : null;
    }

    private static void AddAddressUnitsCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        var addressBuildingCriteria = GetAddressOnlyCriteria(options);
        if (addressBuildingCriteria != null)
        {
            criteria.Add(addressBuildingCriteria);
        }

        var unitNumberCriterias = GetUnitOnlyCriteria(options);
        unitNumberCriterias?.ForEach(criteria.Add);

        var addressBuildingIdAndUnitNumberCriterias = GetAddressUnitCriteria(options);
        addressBuildingIdAndUnitNumberCriterias?.ForEach(criteria.Add);
    }

    private static Func<QueryContainerDescriptor<ListingDocument>, QueryContainer> GetAddressOnlyCriteria(ElasticSearchOptions options)
    {
        if (!(options.AddressUnits?.Length > 0))
        {
            return null;
        }

        var addressBuildingIds = options.AddressUnits.Where(x => x.Unit == null && x.RPBin != null).Select(addressUnit => addressUnit.RPBin).ToList();
        if (addressBuildingIds.Any())
        {
            return c => c.Terms(t => t.Field(f => f.Property.RPBin).Terms(addressBuildingIds));
        }

        return null;
    }

    private static Func<QueryContainerDescriptor<ListingDocument>, QueryContainer> GetSchoolCriteria(ElasticSearchOptions options)
    {
        if (!(options.SchoolIdsWithZone?.Length > 0))
        {
            return null;
        }

        return c => c
            .Terms(t => t
                .Field(f => f.Property.SchoolIds)
                .Terms(options.SchoolIdsWithZone));
    }

    private static Func<QueryContainerDescriptor<ListingDocument>, QueryContainer> GetLineCriteria(ElasticSearchOptions options)
    {
        if (!(options.LineIds?.Length > 0))
        {
            return null;
        }

        return c => c
            .Terms(t => t
                .Field(f => f.Property.LineIds)
                .Terms(options.LineIds));
    }

    private static IReadOnlyCollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> GetUnitOnlyCriteria(ElasticSearchOptions options)
    {
        if (!(options.AddressUnits?.Length > 0) || !options.AddressUnits.Any(x => x.Unit != null && x.RPBin == null))
        {
            return null;
        }

        return options.AddressUnits
            .Where(x => x.Unit != null && x.RPBin == null)
            .Select(addressUnit => (Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>)(m => m
            .QueryString(qs => qs.Fields(df => df.Field(f => f.Unit.UnitNumber.Suffix("keyword"))).AnalyzeWildcard().Analyzer("keyword")
                .Query($"*{addressUnit.Unit.ToUpper().Replace("/", "\\/").Replace("+", "\\+")}*"))))
            .ToList();
    }

    private static IReadOnlyCollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> GetAddressUnitCriteria(ElasticSearchOptions options)
    {
        if (!(options.AddressUnits?.Length > 0) || !options.AddressUnits.Any(x => x.Unit != null && x.RPBin != null))
        {
            return null;
        }

        return options.AddressUnits.Where(x => x.Unit != null && x.RPBin != null)
            .Select(addressUnit => new List<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>>
            {
                m => m.Term(t => t.Field(f => f.Property.RPBin).Value(addressUnit.RPBin)),
                m => m
                    .QueryString(qs => qs.Fields(df => df.Field(f => f.Unit.UnitNumber.Suffix("keyword"))).AnalyzeWildcard().Analyzer("keyword")
                        .Query($"*{addressUnit.Unit.ToUpper().Replace("/", "\\/").Replace("+", "\\+")}*"))
            })
            .Select(auc => (Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>)(m => m.Bool(b => b.Must(auc))))
            .ToList();
    }

    private static void AddMapShapesCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        if (!(options.MapShapes?.Length > 0))
        {
            return;
        }

        var circleCriterias = GetCircleShapesCriteria(options);
        circleCriterias?.ForEach(criteria.Add);

        var polygonCriterias = GetPolygonShapesCriteria(options);
        polygonCriterias?.ForEach(criteria.Add);
    }

    private static IReadOnlyCollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> GetCircleShapesCriteria(ElasticSearchOptions options)
    {
        if (!(options.MapShapes?.Length > 0))
        {
            return null;
        }

        return options.MapShapes.OfType<ElasticSearchOptions.MapCircle>()
            .Select(
                mapCircle => (Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>)(
                    m => m.Bool(b => b.Filter(f => f.GeoDistance(
                        p => p.Field(pf => pf.Property.Coordinates)
                            .Distance(mapCircle.Radius, DistanceUnit.Meters).DistanceType(GeoDistanceType.Arc).Location(mapCircle.Coordinates.First().Latitude, mapCircle.Coordinates.First().Longitude))))))
            .ToList();
    }

    private static IReadOnlyCollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> GetPolygonShapesCriteria(ElasticSearchOptions options)
    {
        if (!(options.MapShapes?.Length > 0))
        {
            return null;
        }

        return options.MapShapes
            .Where(x => !(x is ElasticSearchOptions.MapCircle))
            .Select(mapShape => (Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>)(
                m => m.Bool(
                    b => b.Filter(
                        f => f.GeoPolygon(p => p.Field(pf => pf.Property.Coordinates).Points(mapShape.Coordinates.Select(x => new GeoLocation(x.Latitude, x.Longitude))))))))
            .ToList();
    }

    private static void AddStreetGridsCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        var streetGridCriterias = GetStreetGridsCriteria(options);
        streetGridCriterias?.ForEach(criteria.Add);
    }

    private static IReadOnlyCollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> GetStreetGridsCriteria(ElasticSearchOptions options)
    {
        if (!(options.StreetGrids?.Length > 0))
        {
            return null;
        }

        return options.StreetGrids.Select(streetGrid => new List<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>>
            {
                m => m.Range(r => r.Field(f => f.Property.NorthGridId).LessThanOrEquals(streetGrid.North)),
                m => m.Range(r => r.Field(f => f.Property.SouthGridId).GreaterThanOrEquals(streetGrid.South)),
                m => m.Range(r => r.Field(f => f.Property.WestGridId).LessThanOrEquals(streetGrid.West)),
                m => m.Range(r => r.Field(f => f.Property.EastGridId).GreaterThanOrEquals(streetGrid.East))
            })
            .Select(sgc => (Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>)(m => m.Bool(b => b.Must(sgc))))
            .ToList();
    }

    private static void AddPostalCodeCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        var postalCodeCriteria = GetPostalCodeCriteria(options);
        if (postalCodeCriteria != null)
        {
            criteria.Add(postalCodeCriteria);
        }
    }

    private static Func<QueryContainerDescriptor<ListingDocument>, QueryContainer> GetPostalCodeCriteria(ElasticSearchOptions options)
    {
        if (options.PostCodes?.Length > 0)
        {
            return m => m.Terms(t => t.Field(f => f.Property.PostalCode).Terms(options.PostCodes));
        }
        return null;
    }

    private static void AddWidthCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        if (!(options.WidthMin > 0) && !(options.WidthMax > 0))
        {
            return;
        }

        NumericRangeQueryDescriptor<ListingDocument> RangeQuery(NumericRangeQueryDescriptor<ListingDocument> rq)
        {
            rq.Field(f => f.Property.Width);
            if (options.WidthMin > 0)
            {
                rq.GreaterThanOrEquals(options.WidthMin);
            }

            if (options.WidthMax > 0)
            {
                rq.LessThanOrEquals(options.WidthMax);
            }

            return rq;
        }

        criteria.Add(mo => mo.Bool(bo => bo.Should(new List<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>>
        {
            c => c.Range(RangeQuery)
        })));
    }

    private static void AddUnitsCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        if (!options.UnitsMin.HasValue && !options.UnitsMax.HasValue)
        {
            return;
        }

        NumericRangeQueryDescriptor<ListingDocument> RangeQuery(NumericRangeQueryDescriptor<ListingDocument> rq)
        {
            rq.Field(f => f.Units);
            if (options.UnitsMin.HasValue)
            {
                rq.GreaterThanOrEquals(options.UnitsMin);
            }

            if (options.UnitsMax.HasValue)
            {
                rq.LessThanOrEquals(options.UnitsMax);
            }

            return rq;
        }

        criteria.Add(mo => mo.Bool(bo => bo.Should(new List<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>>
        {
            c => c.Range(RangeQuery)
        })));
    }

    private static void AddBuildingFloorsCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        if (!options.BuildingFloorsMin.HasValue && !options.BuildingFloorsMax.HasValue)
        {
            return;
        }

        NumericRangeQueryDescriptor<ListingDocument> RangeQuery(NumericRangeQueryDescriptor<ListingDocument> rq)
        {
            rq.Field(f => f.Floors);
            if (options.BuildingFloorsMin.HasValue)
            {
                rq.GreaterThanOrEquals(options.BuildingFloorsMin);
            }

            if (options.BuildingFloorsMax.HasValue)
            {
                rq.LessThanOrEquals(options.BuildingFloorsMax);
            }

            return rq;
        }

        criteria.Add(mo => mo.Bool(bo => bo.Should(new List<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>>
        {
            c => c.Range(RangeQuery)
        })));
    }

    private static void AddTownhouseFeaturesCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        if (options.TownhouseFeatures?.Length > 0)
        {
            criteria.Add(m => m.Terms(t => t.Field(f => f.Property.TownhouseFeatures).Terms(options.TownhouseFeatures.Where(feature => PropertyTownhouseFeatures.Contains(feature)).ToList())));
        }
    }

    private static void AddBuildingTypeCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        if (options.BuildingTypes?.Length > 0)
        {
            criteria.Add(m => m.Terms(t => t.Field(f => f.Property.BuildingType).Terms(options.BuildingTypes)));
        }
    }

    private static void AddBuildingAllowedCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        if (!(options.BuildingAllowed?.Length > 0))
        {
            return;
        }

        var buildingAllowIds = options.BuildingAllowed.Select(buildingAllowId => RentalsSalesBuildingAllowIdsMapping.ContainsKey(buildingAllowId)
                ? RentalsSalesBuildingAllowIdsMapping.First(mapping => mapping.Key == buildingAllowId).Value
                : buildingAllowId)
            .ToList();

        IBoolQuery TermQuery(BoolQueryDescriptor<ListingDocument> boolQueryDescriptor)
        {
            var terms = buildingAllowIds.Select(allowed => (Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>)(m => m.Term(t => t.Field(f => f.Property.BuildingAllowed).Value(allowed)))).ToList();
            boolQueryDescriptor.Must(terms);
            return boolQueryDescriptor;
        }

        criteria.Add(c => c.Bool(TermQuery));
    }

    private static void AddBuildingNotAllowedCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        if (!(options.BuildingNotAllowed?.Length > 0))
        {
            return;
        }

        var buildingNotAllowIds = options.BuildingNotAllowed.Select(buildingNotAllowId => RentalsSalesBuildingNotAllowIdsMapping.ContainsKey(buildingNotAllowId)
                ? RentalsSalesBuildingNotAllowIdsMapping.First(mapping => mapping.Key == buildingNotAllowId).Value
                : buildingNotAllowId)
            .ToList();

        IBoolQuery TermQuery(BoolQueryDescriptor<ListingDocument> boolQueryDescriptor)
        {
            var terms = buildingNotAllowIds.Select(notAllowed => (Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>)(m => m.Term(t => t.Field(f => f.Property.BuildingNotAllowed).Value(notAllowed)))).ToList();
            boolQueryDescriptor.Must(terms);
            return boolQueryDescriptor;
        }

        criteria.Add(c => c.Bool(TermQuery));
    }

    private static void AddAmenitiesCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        var amenities = options.Amenities;
        if (!(amenities?.Length > 0))
        {
            return;
        }

        if (options.MiscellaneousDetails != null && Array.IndexOf(options.MiscellaneousDetails, WasherDryerValue) > -1)
        {
            amenities = amenities.Where(o => o != LaundryFacilitiesValue).ToArray();
            if (amenities.Length == 0)
            {
                return;
            }
        }

        IBoolQuery TermQuery(BoolQueryDescriptor<ListingDocument> boolQueryDescriptor)
        {
            var terms = amenities.Select(amenity => (Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>)(m => m.Term(t => t.Field(f => f.Property.BuildingAmenity).Value(amenity)))).ToList();
            boolQueryDescriptor.Must(terms);
            return boolQueryDescriptor;
        }

        criteria.Add(c => c.Bool(TermQuery));
    }

    private static void AddLandLeaseCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        if (!string.IsNullOrEmpty(options.LandLease))
        {
            criteria.Add(m => m.Match(t => t.Field(f => f.Property.LandLease).Query(options.LandLease)));
        }
    }

    private static void AddAttendedLobbyCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        var attendedLobby = options.AttendedLobby;

        if (!(attendedLobby?.Length > 0))
        {
            return;
        }

        IBoolQuery TermQuery(BoolQueryDescriptor<ListingDocument> boolQueryDescriptor)
        {
            var terms = new List<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>>();

            var attendedLobbyAny = attendedLobby.Any(o => o == AttendedLobbyAnyValue);
            var unAttendedLobby = attendedLobby.Any(o => o == UnattendedLobbyValue);
            var hasDoormanTypes = attendedLobby.Any(lobby => lobby == Doorman || Array.IndexOf(DoormanTypes, lobby) > -1);
            attendedLobby = attendedLobby.Where(o => o != AttendedLobbyAnyValue && o != UnattendedLobbyValue).ToArray();

            if (attendedLobbyAny && unAttendedLobby)
            {
                terms.Add(c => c.Bool(b => b.Must(m => m.Terms(t => t.Field(f => f.Property.AttendedLobby).Terms(AttendedLobbyValues.Concat(new[] { UnattendedLobbyValue }))))));
            }
            else if (attendedLobbyAny)
            {
                terms.Add(c => c.Bool(b => b.Must(m => m.Terms(t => t.Field(f => f.Property.AttendedLobby).Terms(AttendedLobbyValues)))));
            }
            else if (unAttendedLobby)
            {
                terms.Add(c => c.Term(t => t.Field(f => f.Property.AttendedLobby).Value(UnattendedLobbyValue)));
            }
            else
            {
                IBoolQuery AndClause(BoolQueryDescriptor<ListingDocument> queryDescriptor)
                {
                    if (hasDoormanTypes)
                    {
                        var doormanTypes = attendedLobby.Where(lobby => Array.IndexOf(DoormanTypes, lobby) > -1).ToList();
                        if (doormanTypes.Count == 0)
                        {
                            doormanTypes.AddRange(DoormanTypes);
                        }
                        attendedLobby = attendedLobby.Where(lobby => lobby != Doorman && Array.IndexOf(DoormanTypes, lobby) == -1).ToArray();
                        queryDescriptor.Must(
                            must => must.Terms(t => t.Field(f => f.Property.AttendedLobby).Terms(doormanTypes)),
                            must => must.Bool(bb => bb.Must(attendedLobby.Select(lobby => (Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>)(m => m.Term(t => t.Field(f => f.Property.AttendedLobby).Value(lobby)))).ToList())));
                    }
                    else
                    {
                        queryDescriptor.Must(attendedLobby.Select(lobby => (Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>)(m => m.Term(t => t.Field(f => f.Property.AttendedLobby).Value(lobby)))));
                    }
                    return queryDescriptor;
                }

                IBoolQuery OrClause(BoolQueryDescriptor<ListingDocument> queryDescriptor)
                {
                    var selectedLobbies = new List<int>(attendedLobby);
                    if (selectedLobbies.Any(lobby => lobby == Doorman))
                    {
                        selectedLobbies = selectedLobbies.Where(lobby => lobby != Doorman).ToList();
                        selectedLobbies.AddRange(DoormanTypes);
                    }
                    queryDescriptor.Must(m => m.Terms(t => t.Field(f => f.Property.AttendedLobby).Terms(selectedLobbies.Distinct())));
                    return queryDescriptor;
                }

                if (options.SearchClause.AttendedLobby == SearchClause.And)
                {
                    terms.Add(c => c.Bool(AndClause));
                }
                else
                {
                    terms.Add(c => c.Bool(OrClause));
                }
            }

            boolQueryDescriptor.Should(terms);
            return boolQueryDescriptor;
        }

        criteria.Add(c => c.Bool(TermQuery));
    }

    private static void AddYearBuiltCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        if (!(options.YearBuiltMin > 0) && !(options.YearBuiltMax > 0))
        {
            return;
        }

        NumericRangeQueryDescriptor<ListingDocument> RangeQuery(NumericRangeQueryDescriptor<ListingDocument> rq)
        {
            rq.Field(f => f.Property.YearBuilt);
            if (options.YearBuiltMin > 0)
            {
                rq.GreaterThanOrEquals(options.YearBuiltMin);
            }

            if (options.YearBuiltMax > 0)
            {
                rq.LessThanOrEquals(options.YearBuiltMax);
            }

            return rq;
        }

        criteria.Add(mo => mo.Bool(bo => bo.Should(new List<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>>
        {
            c => c.Range(RangeQuery)
        })));
    }

    private static void AddManagedBuildingCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        if (options.ManagedBuildings?.Length > 0)
        {
            criteria.Add(m => m.Terms(t => t.Field(f => f.Property.ManagedBuilding).Terms(options.ManagedBuildings)));
        }
    }
}