﻿using Nest;
using RealPlusNLP.Elasticsearch.Contracts;
using RealPlusNLP.Elasticsearch.Documents;
using RealPlusNLP.Elasticsearch.Messages;

namespace RealPlusNLP.Elasticsearch.Creators.Listings;

internal sealed class ElasticSearchUnitQueryUpdater : IElasticSearchQueryUpdater<ListingDocument>
{
    private const int OutdoorSpaceTerraceValue = 213;
    private const int MiscellaneousDetailSponsorApartmentValue = 2573;
    private const int TownhouseOwnershipTypeId = 225;
    private const int ViewAny = 202;
    private const int OutdoorSpaceAny = 206;
    private const int OutdoorSpaceNone = 207;
    private const int LaundryFacilitiesValue = 194;
    private const int WasherDryerValue = 2570;
    private static readonly int[] OutdoorSpaceTerraceValues = { OutdoorSpaceTerraceValue, 3725 };
    private static readonly int[] UnitTownhouseFeatures = { 2598, 2601 };
    private static readonly int[] MiscellaneousEssentials = { 3615, 3616, 3617, 3614, 3618, 3845, 3900 };
    private static readonly IReadOnlyDictionary<int, int> PartialViewsValues = new Dictionary<int, int>()
    {
        [203] = 3813, // City
        [3810] = 3814, // Garden
        [204] = 3815, // Park
        [205] = 3816, //  River
        [3735] = 3817 // Skyline
    };

    private static readonly IReadOnlyDictionary<int, IReadOnlyCollection<int>> TownhouseTypesMapping = new Dictionary<int, IReadOnlyCollection<int>>
    {
        { 2591, new List<int> { 2592, 2593 } },
        { 2594, new List<int> { 2595, 2596 } }
    };

    public void Update(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        AddOwnershipIdCriteria(options, criteria);
        AddBedroomsCriteria(options, criteria);
        AddBathroomsCriteria(options, criteria);
        AddTotalRoomsCriteria(options, criteria);
        AddApartmentTypesCriteria(options, criteria);
        AddSquareFootageCriteria(options, criteria);
        AddFloorCriteria(options, criteria);
        AddViewsCriteria(options, criteria);
        AddExposureCriteria(options, criteria);
        AddMultiFloorCriteria(options, criteria);
        AddTownhouseTypeCriteria(options, criteria);
        AddFurnishedCriteria(options, criteria);
        AddMiscellaneousDetailsCriteria(options, criteria);
        AddOutdoorSpaceCriteria(options, criteria);
    }

    public void UpdateShouldClauses(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
    }

    public void UpdateMustClauses(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        AddTownhouseFeaturesCriteria(options, criteria);
    }

    private static void AddOwnershipIdCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        // ownership
        if (options.OwnershipIds?.Length > 0)
        {
            criteria.Add(m => m.Terms(t => t.Field(f => f.OwnershipId).Terms(options.OwnershipIds)));
        }
    }

    private static void AddBedroomsCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        // bedrooms
        if (!options.BedroomsMin.HasValue && !options.BedroomsMax.HasValue)
        {
            return;
        }

        if (options.BedroomsMin.HasValue && options.BedroomsMore || options.BedroomsMax.HasValue)
        {
            var term = new NumericRangeQueryDescriptor<ListingDocument>().Field(f => f.Bedrooms);
            if (options.BedroomsMin.HasValue)
            {
                term.GreaterThanOrEquals(options.BedroomsMin.Value);
            }

            if (options.BedroomsMax.HasValue)
            {
                term.LessThanOrEquals(options.BedroomsMax.Value);
            }

            criteria.Add(m => m.Range(r => term));
        }
        else
        {
            criteria.Add(m => m.Term(t => t.Field(f => f.Bedrooms).Value(options.BedroomsMin.Value)));
        }
    }

    private static void AddBathroomsCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        // bathrooms
        if (!options.BathroomsMin.HasValue && !options.BathroomsMax.HasValue)
        {
            return;
        }

        if (options.BathroomsMin.HasValue && options.BathroomsMore || options.BathroomsMax.HasValue)
        {
            var term = new NumericRangeQueryDescriptor<ListingDocument>().Field(f => f.Bathrooms);
            if (options.BathroomsMin.HasValue)
            {
                term.GreaterThanOrEquals(options.BathroomsMin.Value);
            }

            if (options.BathroomsMax.HasValue)
            {
                term.LessThanOrEquals(options.BathroomsMax.Value);
            }

            criteria.Add(m => m.Range(r => term));
        }
        else
        {
            criteria.Add(m => m.Term(t => t.Field(f => f.Bathrooms).Value(options.BathroomsMin.Value)));
        }
    }

    private static void AddTotalRoomsCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        // total rooms
        if (!options.TotalRoomsMin.HasValue && !options.TotalRoomsMax.HasValue)
        {
            return;
        }

        if (options.TotalRoomsMin.HasValue && options.TotalRoomsMore || options.TotalRoomsMax.HasValue)
        {
            var term = new NumericRangeQueryDescriptor<ListingDocument>().Field(f => f.TotalRooms);
            if (options.TotalRoomsMin.HasValue)
            {
                term.GreaterThanOrEquals(options.TotalRoomsMin.Value);
            }

            if (options.TotalRoomsMax.HasValue)
            {
                term.LessThanOrEquals(options.TotalRoomsMax.Value);
            }

            criteria.Add(m => m.Range(r => term));
        }
        else
        {
            criteria.Add(m => m.Term(t => t.Field(f => f.TotalRooms).Value(options.TotalRoomsMin.Value)));
        }
    }

    private static void AddApartmentTypesCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        if (options.OwnershipIds != null && options.OwnershipIds.All(ownershipId => ownershipId == TownhouseOwnershipTypeId))
        {
            return;
        }

        // apartment type
        if (options.ApartmentTypes?.Length > 0)
        {
            criteria.Add(m => m.Terms(t => t.Field(f => f.Unit.ApartmentType).Terms(options.ApartmentTypes)));
        }
    }

    private static void AddSquareFootageCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        if (!(options.SqFtMin > 0) && !(options.SqFtMax > 0))
        {
            return;
        }

        NumericRangeQueryDescriptor<ListingDocument> RangeQuery(NumericRangeQueryDescriptor<ListingDocument> rq)
        {
            rq.Field(f => f.SquareFootage);
            if (options.SqFtMin > 0)
            {
                rq.GreaterThanOrEquals(options.SqFtMin);
            }

            if (options.SqFtMax > 0)
            {
                rq.LessThanOrEquals(options.SqFtMax);
            }

            return rq;
        }

        if (options.IncludeMissingSqFt)
        {
            criteria.Add(mo => mo.Bool(bo => bo.Should(new List<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>>
            {
                c => c.Range(RangeQuery),
                c => c.Bool(b => b.MustNot(m => m.Exists(e => e.Field(f => f.SquareFootage)))),
                c => c.Term(t => t.Field(f => f.SquareFootage).Value(0))
            })));
        }
        else
        {
            criteria.Add(mo => mo.Bool(bo => bo.Should(new List<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>>
            {
                c => c.Range(RangeQuery)
            })));
        }
    }

    private static void AddFloorCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        if (options.FloorMin <= 0 && options.FloorMax <= 0 && !options.IncludePenthouse)
        {
            return;
        }

        NumericRangeQueryDescriptor<ListingDocument> RangeQuery(NumericRangeQueryDescriptor<ListingDocument> rq)
        {
            rq.Field(f => f.Unit.Floor);
            if (options.FloorMin > 0)
            {
                rq.GreaterThanOrEquals(options.FloorMin);
            }

            if (options.FloorMax > 0)
            {
                rq.LessThanOrEquals(options.FloorMax);
            }

            return rq;
        }

        var floorCriterias = new List<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>>();

        if (options.IncludePenthouse)
        {
            floorCriterias.Add(c => c.Term(t => t.Field(f => f.Unit.Penthouse).Value(true)));
        }

        if (options.FloorMin > 0 || options.FloorMax > 0)
        {
            floorCriterias.Add(mo => mo.Bool(bo => bo.Should(c => c.Range(RangeQuery))));
        }

        criteria.Add(mo => mo.Bool(bo => bo.Should(floorCriterias)));
    }

    private static void AddViewsCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        if (!(options.Views?.Length > 0))
        {
            return;
        }

        IBoolQuery TermQuery(BoolQueryDescriptor<ListingDocument> boolQueryDescriptor)
        {
            if (options.Views.Any(o => o == ViewAny))
            {
                return boolQueryDescriptor.Must(m => m.Exists(e => e.Field(f => f.Unit.Views)));
            }
            else
            {
                List<int> viewOptions = options.Views.ToList();
                IEnumerable<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> AndClause(IReadOnlyCollection<int> views)
                {
                    var terms = new List<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>>();
                    if (views.Any(view => PartialViewsValues.ContainsKey(view)))
                    {
                        IEnumerable<int[]> partialViews = PartialViewsValues.Where(partial => views.Contains(partial.Key)).Select(partial => new[] { partial.Key, partial.Value });
                        terms.AddRange(partialViews.Select(arr => (Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>)(m => m.Terms(t => t.Field(f => f.Unit.Views).Terms(arr)))));
                    }
                    views = views.Where(view => !PartialViewsValues.ContainsKey(view)).ToList();
                    if (views.Any())
                    {
                        terms.AddRange(views.Select(view => (Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>)(m => m.Term(t => t.Field(f => f.Unit.Views).Value(view)))));
                    }
                    return terms;
                }

                IEnumerable<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> OrClause(IReadOnlyCollection<int> views)
                {
                    IEnumerable<int> partialViews = PartialViewsValues.Where(partial => views.Contains(partial.Key)).Select(partial => partial.Value);
                    List<int> allViews = new List<int>(views);
                    allViews.AddRange(partialViews);
                    return new List<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>>
                    {
                        m => m.Terms(t => t.Field(f => f.Unit.Views).Terms(allViews))
                    };
                }

                boolQueryDescriptor.Must(options.SearchClause.Views == SearchClause.And ? AndClause(viewOptions) : OrClause(viewOptions));

                return boolQueryDescriptor;
            }
        }

        criteria.Add(c => c.Bool(TermQuery));
    }

    private static void AddExposureCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        if (!(options.Exposure?.Length > 0))
        {
            return;
        }

        IBoolQuery TermQuery(BoolQueryDescriptor<ListingDocument> boolQueryDescriptor)
        {
            var terms = options.Exposure.Select(exposure => (Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>)(m => m.Term(t => t.Field(f => f.Unit.Exposure).Value(exposure)))).ToList();
            boolQueryDescriptor.Must(terms);
            return boolQueryDescriptor;
        }

        criteria.Add(c => c.Bool(TermQuery));
    }

    private static void AddMultiFloorCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        if (options.MultiFloor?.Length > 0)
        {
            criteria.Add(m => m.Terms(t => t.Field(f => f.Multifloor).Terms(options.MultiFloor)));
        }
    }

    private static void AddTownhouseTypeCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        if (!(options.TownhouseType?.Length > 0))
        {
            return;
        }

        criteria.Add(m => m.Terms(t => t.Field(f => f.TownhouseType).Terms(options.TownhouseType)));
    }

    private static void AddFurnishedCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        if (options.Furnished?.Length > 0)
        {
            criteria.Add(m => m.Terms(t => t.Field(f => f.Unit.Furnished).Terms(options.Furnished)));
        }
    }

    private static void AddMiscellaneousDetailsCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        var details = options.MiscellaneousDetails?.Where(option => !MiscellaneousEssentials.Contains(option)).ToArray();

        if (!(details?.Length > 0))
        {
            return;
        }

        if (options.Amenities != null && Array.IndexOf(options.Amenities, LaundryFacilitiesValue) > -1)
        {
            details = details.Where(o => o != WasherDryerValue).ToArray();
            if (details.Length == 0)
            {
                return;
            }
        }

        IBoolQuery TermQuery(BoolQueryDescriptor<ListingDocument> boolQueryDescriptor)
        {
            var terms = new List<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>>();

            if (details.Any(detail => detail == MiscellaneousDetailSponsorApartmentValue))
            {
                // Replace Sponsor Apartment misc id with SponsorUnit value check
                details = details.Where(detail => detail != MiscellaneousDetailSponsorApartmentValue).ToArray();
                terms.Add(c => c.Term(t => t.Field(f => f.SponsorUnit).Value(true)));
            }

            // Add other misc details with with AND condition
            terms.AddRange(details.Select(detail => (Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>)(m => m.Term(t => t.Field(f => f.Unit.MiscDeatils).Value(detail)))).ToList());
            boolQueryDescriptor.Must(terms);
            return boolQueryDescriptor;
        }

        criteria.Add(c => c.Bool(TermQuery));
    }

    private static void AddOutdoorSpaceCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        var outdoorSpace = options.OutdoorSpace;

        if (!(outdoorSpace?.Length > 0))
        {
            return;
        }

        IBoolQuery TermQuery(BoolQueryDescriptor<ListingDocument> boolQueryDescriptor)
        {
            if (outdoorSpace.Contains(OutdoorSpaceAny) || outdoorSpace.Contains(OutdoorSpaceNone))
            {
                if (outdoorSpace.Contains(OutdoorSpaceAny))
                {
                    boolQueryDescriptor.Must(m => m
                        .Exists(e => e.Field(f => f.Unit.OutdoorSpace)));
                }

                if (outdoorSpace.Contains(OutdoorSpaceNone))
                {
                    boolQueryDescriptor.MustNot(m => m
                        .Exists(e => e.Field(f => f.Unit.OutdoorSpace)));
                }

                return boolQueryDescriptor;
            }

            IEnumerable<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> AndClause()
            {
                var terms = new List<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>>();
                if (outdoorSpace.Any(outdoor => outdoor == OutdoorSpaceTerraceValue))
                {
                    // Provide OR condition for any terrace type
                    outdoorSpace = outdoorSpace.Where(outdoor => outdoor != OutdoorSpaceTerraceValue).ToArray();
                    terms.Add(m => m.Terms(t => t.Field(f => f.Unit.OutdoorSpace).Terms(OutdoorSpaceTerraceValues)));
                }

                // Provide AND condition for other outdoor types (AND for of any of balcony types)
                terms.AddRange(outdoorSpace.Select(outdoor => (Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>)(m => m.Term(t => t.Field(f => f.Unit.OutdoorSpace).Value(outdoor)))).ToList());
                return terms;
            }

            IEnumerable<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> OrClause()
            {
                List<int> searchValues = new List<int>();
                if (outdoorSpace.Any(outdoor => outdoor == OutdoorSpaceTerraceValue))
                {
                    outdoorSpace = outdoorSpace.Where(outdoor => outdoor != OutdoorSpaceTerraceValue).ToArray();
                    searchValues.AddRange(OutdoorSpaceTerraceValues);
                }
                searchValues.AddRange(outdoorSpace);
                return new List<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>>
                {
                    m => m.Terms(t => t.Field(f => f.Unit.OutdoorSpace).Terms(searchValues))
                };
            }

            boolQueryDescriptor.Must(options.SearchClause.PrivateOutdoorSpace == SearchClause.And ? AndClause() : OrClause());
            return boolQueryDescriptor;
        }

        criteria.Add(c => c.Bool(TermQuery));
    }

    private static void AddTownhouseFeaturesCriteria(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> criteria)
    {
        if (!(options.TownhouseFeatures?.Length > 0))
        {
            return;
        }

        var features = options.TownhouseFeatures.Where(feature => UnitTownhouseFeatures.Contains(feature)).ToList();

        if (!(features.Count > 0))
        {
            return;
        }

        IBoolQuery TermQuery(BoolQueryDescriptor<ListingDocument> boolQueryDescriptor)
        {
            var terms = features.Select(feature => (Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>)(m => m.Term(t => t.Field(f => f.Unit.TownhouseFeatures).Value(feature)))).ToList();
            boolQueryDescriptor.Must(terms);
            return boolQueryDescriptor;
        }

        criteria.Add(c => c.Bool(TermQuery));
    }
}