﻿using Nest;
using RealPlusNLP.Elasticsearch.Contracts;
using RealPlusNLP.Elasticsearch.Documents;
using RealPlusNLP.Elasticsearch.Messages;

namespace RealPlusNLP.Elasticsearch.Creators;

internal sealed class ElasticSearchListingQueryCreator(IReadOnlyCollection<IElasticSearchQueryUpdater<ListingDocument>> elasticSearchQueryUpdaters) 
    : IElasticSearchQueryCreator<ListingDocument>
{
    private readonly IReadOnlyCollection<IElasticSearchQueryUpdater<ListingDocument>> elasticSearchQueryUpdaters = elasticSearchQueryUpdaters;

    public IReadOnlyCollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> Create(ElasticSearchOptions options)
    {
        var mainCriteria = new List<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>>
        {
            m => m.Term(t => t.Field(f => f.CategoryId).Value(options.CategoryId)),  // category
            m => m.Bool(b => b.Should(new List<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> // security
            {
                mi => mi.Terms(t => t.Field(f => f.SecureGroup).Terms(options.SecureGroupTerms())),
                mi => mi.Bool(bi => bi
                    .Must(mu => mu.Terms(t => t.Field(f => f.SecureGroup).Terms(options.SecureGroups)))
                    .MustNot(new List<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>>
                    {
                        mn => mn.Terms(t => t.Field(f => f.ListingCompany).Terms(options.SecureGroupTerms())),
                        mn => mn.Terms(t => t.Field(f => f.HasCopyForCompany).Terms(options.SecureGroupTerms()))
                    }))
            }))
        };

        foreach (var elasticSearchQueryUpdater in elasticSearchQueryUpdaters)
        {
            elasticSearchQueryUpdater.Update(options, mainCriteria);
        }

        AddCommonShouldClauseCriterias(options, mainCriteria);
        AddCommonMustClauseCriterias(options, mainCriteria);

        return mainCriteria;
    }

    private void AddCommonShouldClauseCriterias(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> mainCriteria)
    {
        var shouldClausesCriteria = new List<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>>();
        foreach (var elasticSearchQueryUpdater in elasticSearchQueryUpdaters)
        {
            elasticSearchQueryUpdater.UpdateShouldClauses(options, shouldClausesCriteria);
        }

        if (shouldClausesCriteria.Count > 0)
        {
            mainCriteria.Add(m => m.Bool(b => b.Should(shouldClausesCriteria)));
        }
    }

    private void AddCommonMustClauseCriterias(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>> mainCriteria)
    {
        var mustClausesCriteria = new List<Func<QueryContainerDescriptor<ListingDocument>, QueryContainer>>();
        foreach (var elasticSearchQueryUpdater in elasticSearchQueryUpdaters)
        {
            elasticSearchQueryUpdater.UpdateMustClauses(options, mustClausesCriteria);
        }

        if (mustClausesCriteria.Count > 0)
        {
            mainCriteria.Add(m => m.Bool(b => b.Must(mustClausesCriteria)));
        }
    }
}