﻿using Nest;

namespace RealPlusNLP.Elasticsearch.Documents;

public class ListingDocument
{
    [Number(NumberType.Integer)]
    public int ListingId { get; set; }
    [Number(NumberType.Integer)]
    public int CategoryId { get; set; }
    [Number(NumberType.Integer)]
    public int StatusId { get; set; }
    [Number(NumberType.Integer)]
    public int? OwnershipId { get; set; }
    [Keyword(Index = true)]
    public string ListingNumber { get; set; }
    [Keyword(Index = true)]
    public string WebId { get; set; }
    [Keyword(Index = true)]
    public string ListingCompany { get; set; }
    [Keyword(Index = true)]
    public string ListingCompanyName { get; set; }
    [Keyword(Index = true)]
    public string SecureGroup { get; set; }
    [Keyword]
    public string[] HasCopyForCompany { get; set; }
    [Number(NumberType.Double)]
    public double? Price { get; set; }
    [Number(NumberType.Double)]
    public double? ClosingPrice { get; set; }
    [Number(NumberType.Double)]
    public double? MonthlyExpence { get; set; }
    [Number(NumberType.Double)]
    public double? PricePerSqFt { get; set; }
    [Number(NumberType.Double, Index = false)]
    public double? PricePerRoom { get; set; }

    [Number(NumberType.Integer)]
    public int? FeePaidBy { get; set; }
    [Boolean]
    public bool SponsorUnit { get; set; }
    [Number(NumberType.Integer)]
    public int? ListingType { get; set; }
    [Number(NumberType.Integer)]
    public int? LeaseType { get; set; }
    [Number(NumberType.Integer)]
    public int? MinLeaseTerm { get; set; }
    [Number(NumberType.Integer)]
    public int? MaxLeaseTerm { get; set; }

    // from unit/building level
    [Number(NumberType.Integer)]
    public int? Bedrooms { get; set; }
    [Number(NumberType.Double)]
    public double? Bathrooms { get; set; }
    [Number(NumberType.Double, Index = false)]
    public double? FullBathrooms { get; set; }
    [Number(NumberType.Double, Index = false)]
    public double? HalfBathrooms { get; set; }
    [Number(NumberType.Double)]
    public double? TotalRooms { get; set; }
    [Number(NumberType.Integer)]
    public int? SquareFootage { get; set; }
    [Text(Index = false)]
    public string ApproxExteriorSqFt { get; set; }
    [Number(NumberType.Integer)]
    public int? Units { get; set; }
    [Text(Index = false)]
    public string BuildingSize { get; set; }
    [Number(NumberType.Integer)]
    public int? Floors { get; set; }
    [Number(NumberType.Integer)]
    public int? Multifloor { get; set; }
    public int? TownhouseType { get; set; }
    [Text(Index = false)]
    public string TownhouseTypeText { get; set; }
    [Object]
    public IndexedListingOffer FreeRent { get; set; }
    [Object]
    public ListingOffer OwnerPays { get; set; }
    [Object]
    public ListingOffer BonusOffered { get; set; }
    [Object]
    public ListingAirRights AirRights { get; set; }
    [Text(Index = false)]
    public string BuildingClass { get; set; }
    [Text(Index = false)]
    public string Zoning { get; set; }
    [Text(Index = false)]
    public string ShowingInstructions { get; set; }
    [Text(Index = false)]
    public string AccessInstructions { get; set; }
    [Text(Index = false)]
    public string DealRequirements { get; set; }
    [Text(Index = false)]
    public string AdditionalPetPolicy { get; set; }
    [Date]
    public DateTime? ListDate { get; set; }
    [Date]
    public DateTime? UpdateDate { get; set; }
    [Date]
    public DateTime? ExpirationDate { get; set; }
    [Date]
    public DateTime? ContractSignDate { get; set; }
    [Date]
    public DateTime? CloseDate { get; set; }
    [Date]
    public DateTime? ListedActivityDate { get; set; }
    [Date]
    public DateTime? SoldActivityDate { get; set; }

    [Boolean]
    public bool ContractSigned { get; set; }
    [Boolean]
    public bool Closed { get; set; }
    [Boolean]
    public bool Verified { get; set; }
    [Number(NumberType.Integer, Index = false)]
    public int? RentSaleListingId { get; set; }
    [Boolean]
    public bool HasRentSaleListing { get; set; }
    [Boolean]
    public bool? CollectYourOwnFee { get; set; }
    [Keyword(Index = true)]
    public string CoBrokeAgreement { get; set; }
    [Keyword]
    public string[] KeyLocationOffices { get; set; }

    [Text(Store = true)]
    public string Notes { get; set; }
    [Text(Store = true)]
    public string Comments { get; set; }

    public ICollection<OpenHouse> OpenHouses { get; set; }

    [Object]
    public ListingContact Contact { get; set; }
    [Object]
    public PriceChange ListingPriceChange { get; set; }
    [Object]
    public UnitDocument Unit { get; set; }
    [Object]
    public ListingPropertyDocument Property { get; set; }
    public FinancedACRIS ACRISAdditionals { get; set; }
    public string ResaleApartment { get; set; }

    public class FinancedACRIS
    {
        public ICollection<VerifiedPerson> VerifiedBuyers { get; set; }
        public ICollection<VerifiedPerson> VerifiedSellers { get; set; }
        public bool? HasACRISDocumentLinks { get; set; }
        public bool? HasACRISMortgageLinks { get; set; }
        public bool? DealFinanced { get; set; }
    }

    public class VerifiedPerson
    {
        [Text(Index = false)]
        public string FirstName { get; set; }
        [Text(Index = false)]
        public string LastName { get; set; }
        [Text(Index = false)]
        public string AddressLine1 { get; set; }
        [Text(Index = false)]
        public string AddressLine2 { get; set; }
        [Text(Index = false)]
        public string City { get; set; }
        [Text(Index = false)]
        public string State { get; set; }
        [Text(Index = false)]
        public string PostalCode { get; set; }
    }

    public class PriceChange
    {
        [Date]
        public DateTime ChangeDate { get; set; }
        [Keyword(Index = true)]
        public string ChangeDirection { get; set; }
        [Number(NumberType.Double)]
        public double? ChangePercent { get; set; }
    }

    public class ListingContact
    {
        public int[] OfficeIds { get; set; }
        public int[] AgentIds { get; set; }
        public string[] Names { get; set; }
        [Keyword]
        public string[] Emails { get; set; }
        [Keyword]
        public string[] Phones { get; set; }
        [Keyword]
        public string[] DealContactSecureGroups { get; set; }
    }

    public class ListingFreeRent
    {
        [Boolean]
        public bool? FreeRent { get; set; }
        [Date(Index = false)]
        public DateTime? StartDate { get; set; }
        [Date(Index = false)]
        public DateTime? ExpirationDate { get; set; }
        [Text(Index = false)]
        public string Description { get; set; }
    }
    public class OpenHouse
    {
        [Ignore]
        public int ListingId { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        [Text(Index = false)]
        public string Comment { get; set; }
        [Boolean(Index = false)]
        public bool AppointmentOnly { get; set; }
        [Number(NumberType.Integer, Index = false)]
        public int SortTime { get; set; }
        [Number(NumberType.Integer)]
        public OpenHouseType OpenHouseType { get; set; }

        [Ignore]
        public string FormattedDate => $"{StartDate:MM/dd/yyyy h:mm} to {EndDate:h:mm}" + (string.IsNullOrEmpty(Comment) ? "" : Comment);
        [Ignore]
        public long SortDate => StartDate.Ticks;
    }

    // Search Results Listing Data
    [Number(NumberType.Double, Index = false)]
    public double? OriginalPrice { get; set; }
    [Keyword(Index = true)]
    public string CurrentStatus { get; set; }
    [Text(Index = false)]
    public string CommissionRemarks { get; set; }
    [Number(NumberType.Double, Index = false)]
    public double? MaintCc { get; set; }
    [Number(Index = true)]
    public float? ReTaxes { get; set; }
    [Date(Index = false)]
    public DateTime? RentDate { get; set; }
    [Keyword]
    public string LeaseTerm { get; set; }
    [Keyword]
    public string GuarantorsAllowed { get; set; }
    [Date(Index = false)]
    public DateTime? AvailableDate { get; set; }
    [Date(Index = false)]
    public DateTime? VerifiedDate { get; set; }
    [Text(Index = false)]
    public string AcrisId { get; set; }

    [Number(NumberType.Integer, Index = false)]
    public int? Shares { get; set; }
    [Number(NumberType.Double, Index = false)]
    public double? FurnishedPrice { get; set; }
    [Number(NumberType.Double, Index = false)]
    public double? SoldPrice { get; set; }
    [Number(NumberType.Double, Index = false)]
    public double? LastAskPrice { get; set; }

    [Text(Index = false)]
    public string OP { get; set; }
    [Text(Index = false)]
    public string LeaseTypeText { get; set; }
    [Text(Index = false)]
    public string EmailAddress { get; set; }
    [Text(Index = false)]
    public string FullPhoneNumber { get; set; }
    [Text(Index = false)]
    public string FullName { get; set; }

    [Number(NumberType.Integer, Index = false)]
    public int? DOM { get; set; }
    [Number(NumberType.Integer, Index = false)]
    public int TotalDaysOffMarket { get; set; }
    [Number(NumberType.Integer, Index = false)]
    public int DaysOnQuarantine { get; set; }

    [Number(NumberType.Integer, Index = false)]
    public int? DaysOffMarket { get; set; }
    [Text(Index = false)]
    public string ListingTypeText { get; set; }
    [Text(Index = false)]
    public string ListingCategory { get; set; }
    [Text(Index = false)]
    public string ListingCompanyNameText { get; set; }

    [Keyword(Index = true)]
    public string AcrisPropertyType { get; set; }
    [Keyword(Index = true)]
    public string NonMarketSale { get; set; }
    [Text(Index = false)]
    public string SponsorUnitText { get; set; }

    [Object]
    public MediaAsset MediaAsset { get; set; }
}
public class ListingOffer
{
    [Boolean(Index = false)]
    public bool? IsOffered { get; set; }
    [Date(Index = false)]
    public DateTime? StartDate { get; set; }
    [Date(Index = false)]
    public DateTime? ExpirationDate { get; set; }
    [Text(Index = false)]
    public string Description { get; set; }
    public string AndOr { get; set; }
}
public class IndexedListingOffer : ListingOffer
{
    [Boolean]
    public new bool? IsOffered { get; set; }
}

public class ListingAirRights
{
    [Text(Index = false)]
    public string AirRights { get; set; }
    [Date(Index = false)]
    public double? SqFt { get; set; }
}

public class MediaAsset
{
    [Boolean]
    public bool HasTour { get; set; }
    [Boolean]
    public bool HasFloorplan { get; set; }
    [Boolean]
    public bool HasPhoto { get; set; }
    [Boolean]
    public bool HasVideo { get; set; }
}

public class UnitDocument
{
    [Number(NumberType.Integer)]
    public int UnitId { get; set; }
    [Number(NumberType.Integer)]
    public int TBUnitId { get; set; }
    public string UnitNumber { get; set; }
    [Number(NumberType.Integer)]
    public int? OwnershipId { get; set; }
    [Number(NumberType.Integer)]
    public int? ApartmentType { get; set; }
    public int[] MiscEssentials { get; set; }
    public int[] MiscDeatils { get; set; }
    public int[] Furnished { get; set; }
    [Number(NumberType.Integer)]
    public int? Floor { get; set; }
    [Boolean]
    public bool? Penthouse { get; set; }
    public int[] OutdoorSpace { get; set; }
    public int[] Views { get; set; }
    public int[] Exposure { get; set; }
    public int[] TownhouseFeatures { get; set; }
    public bool WithFireplace { get; set; }

    [Number(NumberType.Double, Index = false)]
    public double? MaidBath { get; set; }
    [Number(NumberType.Double, Index = false)]
    public double? MaidBed { get; set; }
    [Text(Index = false)]
    public string HasWashDry { get; set; }
    [Text(Index = false)]
    public string HasFireplace { get; set; }
    [Text(Index = false)]
    public string HeatType { get; set; }
    [Text(Index = false)]
    public string WasherDryer { get; set; }
    [Text(Index = false)]
    public string Condition { get; set; }
    [Text(Index = false)]
    public string ConditionText { get; set; }
    [Text(Index = false)]
    public string HasAc { get; set; }
    [Text(Index = false)]
    public string GasFireplace { get; set; }
    [Text(Index = false)]
    public string WoodBurningFireplace { get; set; }
    [Text(Index = false)]
    public string DecorativeFireplace { get; set; }
    [Number(NumberType.Integer, Index = false)]
    public int? FloorNumber { get; set; }
    [Text(Index = false)]
    public string FloorLine { get; set; }
    [Text(Index = false)]
    public string IsFurnished { get; set; }
    [Text(Index = false)]
    public string ExposureText { get; set; }
    [Text(Index = false)]
    public string ViewsText { get; set; }
    [Text(Index = false)]
    public string OutdoorSpaceText { get; set; }
    [Text(Index = false)]
    public string ApartmentTypeText { get; set; }
    [Text(Index = false)]
    public string GarageType { get; set; }
}

public class ListingPropertyDocument
{
    [Number(NumberType.Integer)]
    public int PropertyId { get; set; }
    [Number(NumberType.Integer)]
    public int TBPropertyId { get; set; }
    [Number(NumberType.Integer)]
    public int? OwnershipId { get; set; }
    [Number(NumberType.Integer)]
    public int? NeighborhoodId { get; set; }
    [Number(NumberType.Integer)]
    public int? RPBin { get; set; }
    [Number(NumberType.Integer)]
    public int? NorthGridId { get; set; }
    [Number(NumberType.Integer)]
    public int? SouthGridId { get; set; }
    [Number(NumberType.Integer)]
    public int? WestGridId { get; set; }
    [Number(NumberType.Integer)]
    public int? EastGridId { get; set; }
    [GeoPoint]
    public GeoCoordinates Coordinates { get; set; }
    [Number(NumberType.Integer)]
    public int? BuildingPeriod { get; set; }
    [Keyword(Index = true)]
    public string PostalCode { get; set; }
    public int[] TownhouseFeatures { get; set; }
    [Number(NumberType.Double)]
    public double? Width { get; set; }
    [Number(NumberType.Integer)]
    public int? BuildingType { get; set; }
    public int[] SchoolIds { get; set; }
    public int[] LineIds { get; set; }
    public int[] AttendedLobby { get; set; }
    public int[] BuildingAllowed { get; set; }
    public int[] BuildingNotAllowed { get; set; }
    public int[] BuildingAmenity { get; set; }
    [Number(NumberType.Integer)]
    public int? ManagedBuilding { get; set; }
    [Number(NumberType.Integer)]
    public int? YearBuilt { get; set; }
    public string LandLease { get; set; }

    [Text(Index = false)]
    public string PetsAllowed { get; set; }
    [Text(Index = false)]
    public string SubletAllowed { get; set; }
    [Text(Index = false)]
    public string Basement { get; set; }
    [Number(NumberType.Integer, Index = false)]
    public int? Elevator { get; set; }
    [Number(NumberType.Double)]
    public double? FinanceAllowedPercent { get; set; }
    [Text(Index = false)]
    public string MaximumFinancingRemarks { get; set; }
    [Number(NumberType.Double, Index = false)]
    public double? TaxDeductionPct { get; set; }
    [Number(NumberType.Double, Index = false)]
    public double? YearIncorporated { get; set; }
    [Text(Index = false)]
    public string FlipTax { get; set; }
    [Keyword(Index = true)]
    public string Neighborhood { get; set; }
    [Text(Index = false)]
    public string HasStorage { get; set; }
    [Text(Index = false)]
    public string HasLaundry { get; set; }
    [Keyword]
    public string[] LaundryFeatures { get; set; }

    [Text(Index = false)]
    public string HasParking { get; set; }
    [Text(Index = false)]
    public string HasHealthClub { get; set; }
    [Text(Index = false)]
    public string LotSize { get; set; }
    [Text(Index = false)]
    public string BuildingName { get; set; }
    [Text(Index = false)]
    public string CrossStreets { get; set; }
    [Text(Index = false)]
    public string HasLobbyAttendant { get; set; }
    [Text(Index = false)]
    public string TypeOfAc { get; set; }
    [Text(Index = false)]
    public string BuildingExtension { get; set; }
    [Text(Index = false)]
    public string ExtensionSize { get; set; }
    [Number(NumberType.Integer, Index = false)]
    public int? HouseNumberSort { get; set; }
    [Number(NumberType.Integer, Index = false)]
    public int PostalAddressId { get; set; }
    [Text(Index = false)]
    public string Suffix { get; set; }
    [Text(Index = false)]
    public string Prefix { get; set; }
    [Keyword(Index = true)]
    public string Address { get; set; }
    [Text(Index = false)]
    public string StreetName { get; set; }
    [Text(Index = false)]
    public string OwnershipType { get; set; }
    [Keyword(Index = true)]
    public string ManhattanGridSort { get; set; }

    [Text(Store = true)]
    public string Notes { get; set; }
    [Text(Store = true)]
    public string Comments { get; set; }
    [Keyword]
    public string MarketedBy { get; set; }
    [Text(Index = false)]
    public string MarketedByText { get; set; }

    [Text(Index = false)]
    public string HasPool { get; set; }
    [Text(Index = false)]
    public string BuildingFeatures { get; set; }
    [Text(Index = false)]
    public string BuildingNotAllowText { get; set; }
    [Text(Index = false)]
    public string BuildingAllowText { get; set; }
    [Text(Index = false)]
    public string BuildingPeriodText { get; set; }
    [Boolean]
    public bool? Resale { get; set; }
    [Boolean]
    public bool? NewDevelopment { get; set; }
    [Number]
    public int? ConstructionType { get; set; }
    [Text(Index = false)]
    public string ConstructionTypeText { get; set; }
    public int[] CommonOutdoorSpace { get; set; }
    public string[] ManagementCompany { get; set; }
    [Keyword]
    public string[] AlternativeManagementCompanies { get; set; }
    public string Owner { get; set; }
    public string Landlord { get; set; }
    [Text(Index = false)]
    public string Doorman { get; set; }
    [Text(Index = false)]
    public string DoormanRemarks { get; set; }
    [Text(Index = false)]
    public string CoopName { get; set; }

    public class GeoCoordinates
    {
        [Number(NumberType.Double)]
        public double Lat { get; set; }
        [Number(NumberType.Double)]
        public double Lon { get; set; }
    }
}

[Flags]
public enum OpenHouseType
{
    None = 0,
    Any = 1,
    Public = 2,
    BrokerOnly = 4,
    ByAppointment = 8,
    Virtual = 16
}
