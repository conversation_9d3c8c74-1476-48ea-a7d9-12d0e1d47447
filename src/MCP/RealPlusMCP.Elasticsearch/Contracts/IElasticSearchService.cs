﻿using RealPlusNLP.Elasticsearch.Documents;
using RealPlusNLP.Elasticsearch.Messages;

namespace RealPlusNLP.Elasticsearch.Contracts;

public interface IElasticSearchService
{
    Task<ElasticSearchResponse<ListingDocument>> GetListingSearchCountAsync(ElasticSearchOptions options);
    Task<ElasticSearchResponse<ListingDocument>> GetListingSearchResultsAsync(ElasticSearchOptions options, int size = 100);
}
