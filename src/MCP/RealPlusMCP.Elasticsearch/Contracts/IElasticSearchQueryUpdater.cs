﻿using Nest;
using RealPlusNLP.Elasticsearch.Messages;

namespace RealPlusNLP.Elasticsearch.Contracts;

internal interface IElasticSearchQueryUpdater<TDocument>
    where TDocument : class
{
    void Update(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<TDocument>, QueryContainer>> criteria);
    void UpdateShouldClauses(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<TDocument>, QueryContainer>> criteria);
    void UpdateMustClauses(ElasticSearchOptions options, ICollection<Func<QueryContainerDescriptor<TDocument>, QueryContainer>> criteria);
}