param location string
@minLength(3)
param env string
param customSubDomainName string
param managedIdentityId string
@secure()
param managedIdentityPrincipalId string
param principalId string
@minLength(3)
param name string
param openAiDeploymentCapacity int
param keyVaultName string
param clientId string

// Reference to existing Key Vault
resource keyVault 'Microsoft.KeyVault/vaults@2023-07-01' existing = {
  name: keyVaultName
}

resource openAi 'Microsoft.CognitiveServices/accounts@2024-10-01' = {
  name: '${name}-${env}'
  location: location
  kind: 'AIServices'
  sku: {
    name: 'S0'
  }
  properties: {
    customSubDomainName: '${customSubDomainName}-${env}'
    publicNetworkAccess: 'Enabled'
    networkAcls: {
      defaultAction: 'Allow'
      virtualNetworkRules: []
      ipRules: []
    }
  }
  identity: {
    type: 'UserAssigned'
    userAssignedIdentities: {
      '${managedIdentityId}': {}
    }
  }
}

resource gpt4oDeployment 'Microsoft.CognitiveServices/accounts/deployments@2024-10-01' = {
  parent: openAi
  name: 'gpt-4o'
  sku: {
    name: 'Standard'
    capacity: openAiDeploymentCapacity
  }
  properties: {
    model: {
      format: 'OpenAI'
      name: 'gpt-4o'
    }
    raiPolicyName: 'Microsoft.DefaultV2'
  }
}
resource gpt4oRealtimeDeployment 'Microsoft.CognitiveServices/accounts/deployments@2024-10-01' = {
  parent: openAi
  name: 'gpt-4o-realtime-preview'
  dependsOn: [
    gpt4oDeployment
  ]
  sku: {
    name: 'GlobalStandard'
    capacity: 6
  }
  properties: {
    model: {
      format: 'OpenAI'
      name: 'gpt-4o-realtime-preview'
      version: '2024-12-17'
    }
    raiPolicyName: 'Microsoft.DefaultV2'
  }
}
resource textEmbedDeployment 'Microsoft.CognitiveServices/accounts/deployments@2024-10-01' = {
  parent: openAi
  name: 'text-embedding-ada-002'
  dependsOn: [
    gpt4oRealtimeDeployment
  ]
  sku: {
    name: 'Standard'
    capacity: openAiDeploymentCapacity
  }
  properties: {
    model: {
      format: 'OpenAI'
      name: 'text-embedding-ada-002'
    }
    raiPolicyName: 'Microsoft.DefaultV2'
  }
}

// Assign Cognitive Services User role to the managed identity
resource cognitiveServicesUserRole 'Microsoft.Authorization/roleAssignments@2022-04-01' = {
  name: guid(openAi.id, managedIdentityPrincipalId, 'Cognitive Services User')
  scope: openAi
  properties: {
    roleDefinitionId: subscriptionResourceId(
      'Microsoft.Authorization/roleDefinitions',
      '5e0bd9bd-7b93-4f28-af87-19fc36ad61bd'
    )
    principalId: managedIdentityPrincipalId
    principalType: 'ServicePrincipal'
    description: 'Allows managed identity to use OpenAI service'
  }
}
// Assign Cognitive Services User role to the admin user
resource adminCognitiveServicesUserRole 'Microsoft.Authorization/roleAssignments@2022-04-01' = {
  name: guid(openAi.id, principalId, 'Cognitive Services Admin User')
  scope: openAi
  properties: {
    roleDefinitionId: subscriptionResourceId(
      'Microsoft.Authorization/roleDefinitions',
      '5e0bd9bd-7b93-4f28-af87-19fc36ad61bd'
    )
    principalId: principalId
    principalType: 'User'
    description: 'Allows admin to use OpenAI service'
  }
}

// Store Azure client ID and tenant ID in Key Vault
resource azureClientId 'Microsoft.KeyVault/vaults/secrets@2023-07-01' = {
  name: 'azure-client-id'
  parent: keyVault
  properties: {
    value: clientId
  }
}
resource azureTenantId 'Microsoft.KeyVault/vaults/secrets@2023-07-01' = {
  name: 'azure-tenant-id'
  parent: keyVault
  properties: {
    value: subscription().tenantId
  }
}

output id string = openAi.id
output endpoint string = openAi.properties.endpoint
output name string = openAi.name
