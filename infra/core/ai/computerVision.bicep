param location string
@minLength(3)
param env string
param customSubDomainName string
param managedIdentityId string
@secure()
param managedIdentityPrincipalId string
param principalId string
@minLength(3)
param name string
param keyVaultName string

// Reference to existing Key Vault
resource keyVault 'Microsoft.KeyVault/vaults@2023-07-01' existing = {
  name: keyVaultName
}

resource computerVision 'Microsoft.CognitiveServices/accounts@2024-10-01' = {
  name: '${name}-${env}'
  // location: location
  location: 'eastus' // Override location for Computer Vision
  kind: 'ComputerVision'
  sku: {
    name: 'S1'
  }
  properties: {
    customSubDomainName: '${customSubDomainName}-${env}'
    publicNetworkAccess: 'Enabled'
    networkAcls: {
      defaultAction: 'Allow'
      virtualNetworkRules: []
      ipRules: []
    }
  }
  identity: {
    type: 'UserAssigned'
    userAssignedIdentities: {
      '${managedIdentityId}': {}
    }
  }
}

// Assign Cognitive Services User role to the managed identity
resource cognitiveServicesUserRole 'Microsoft.Authorization/roleAssignments@2022-04-01' = {
  name: guid(computerVision.id, managedIdentityPrincipalId, 'Cognitive Services User')
  scope: computerVision
  properties: {
    roleDefinitionId: subscriptionResourceId(
      'Microsoft.Authorization/roleDefinitions',
      '5e0bd9bd-7b93-4f28-af87-19fc36ad61bd'
    )
    principalId: managedIdentityPrincipalId
    principalType: 'ServicePrincipal'
    description: 'Allows managed identity to use Computer Vision service'
  }
}

// Assign Cognitive Services User role to the admin user
resource adminCognitiveServicesUserRole 'Microsoft.Authorization/roleAssignments@2022-04-01' = {
  name: guid(computerVision.id, principalId, 'Cognitive Services Admin User')
  scope: computerVision
  properties: {
    roleDefinitionId: subscriptionResourceId(
      'Microsoft.Authorization/roleDefinitions',
      '5e0bd9bd-7b93-4f28-af87-19fc36ad61bd'
    )
    principalId: principalId
    principalType: 'User'
    description: 'Allows admin to use Computer Vision service'
  }
}

// Store Computer Vision credentials in Key Vault
resource computerVisionEndpoint 'Microsoft.KeyVault/vaults/secrets@2023-07-01' = {
  name: 'computer-vision-endpoint'
  parent: keyVault
  properties: {
    value: computerVision.properties.endpoint
  }
}

output id string = computerVision.id
output endpoint string = computerVision.properties.endpoint
output name string = computerVision.name
