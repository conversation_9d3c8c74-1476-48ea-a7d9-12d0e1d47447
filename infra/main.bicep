targetScope = 'subscription'

@description('The location for all resources')
param location string
@description('The environment (dev/prod)')
@allowed([
  'dev'
  'prod'
])
param env string = 'dev'
@description('Principal ID for managed identity')
param principalId string

@description('The number of days to retain log data')
param logAnalyticsRetentionInDays int
@description('The name of the log analytics workspace')
param logAnalyticsName string
@description('The name of the key vault')
param keyVaultName string
@description('The name of the application insights')
param appInsightsName string
@description('Name for OpenAI service')
param openAiName string
@description('Deployment capacity for OpenAI service')
param openAiDeploymentCapacity int
@description('Custom subdomain name for OpenAI service')
param openAiCustomSubDomainName string
@description('Name for Computer Vision service')
param computerVisionName string
@description('Custom subdomain name for Computer Vision service')
param computerVisionCustomSubDomainName string
@description('Name for container registry')
param containerRegistryName string
@description('Name for container apps environment')
param containerAppEnvName string
@description('Name for NLP Api container app')
param containerAppNLPApiName string
@description('Name for Media Search Web container app')
param containerAppMediaSearchWebName string
@description('Name for Listings Similarity Web container app')
param containerAppListingsSimilarityWebName string
@description('Name for Realtime Form Web container app')
param containerAppRealtimeFormWebName string
@description('Name for DB Chat Pro Web container app')
param containerAppDBChatProWebName string
@description('Minimum number of replicas for container app')
param containerAppMinReplicas int
@description('Maximum number of replicas for container app')
param containerAppMaxReplicas int
@description('Container app revision mode')
param containerAppRevisionMode string
@description('Name for MCP Api container app')
param containerAppMCPApiName string
@description('NLP Api Container app exists')
param containerAppNLPApiExists bool
@description('Listings Similarity Web Container app exists')
param containerAppMediaSearchWebExists bool
@description('Media Search Web Container app exists')
param containerAppListingsSimilarityWebExists bool
@description('Realtime Form Web Container app exists')
param containerAppRealtimeFormWebExists bool
@description('DB Chat Pro Web Container app exists')
param containerAppDBChatProWebExists bool
@description('MCP Api Container app exists')
param containerAppMCPApiExists bool
@description('New Relic API key')
param newrelicApiKey string

// Resource name variables
var rgName = 'rg-ai-foundry-realplus-${env}'
var rgTags = {
  environment: env
  application: 'RealPlus AI Foundry'
}

// Create resource group
resource resourceGroup 'Microsoft.Resources/resourceGroups@2022-09-01' = {
  name: rgName
  location: location
  tags: rgTags
}

// Deploy Log Analytics
module logAnalytics 'core/monitoring/logAnalytics.bicep' = {
  name: 'logAnalytics-${uniqueString(subscription().id)}'
  scope: resourceGroup
  params: {
    name: logAnalyticsName
    location: location
    env: env
    retentionInDays: logAnalyticsRetentionInDays
  }
}

// Deploy Managed Identity
module managedIdentity 'core/security/managedIdentity.bicep' = {
  name: 'managedIdentity-${uniqueString(subscription().id)}'
  scope: resourceGroup
  params: {
    location: location
    env: env
  }
}

// Deploy Key Vault
module keyVault 'core/security/keyVault.bicep' = {
  name: 'keyVault-${uniqueString(subscription().id)}'
  scope: resourceGroup
  params: {
    location: location
    env: env
    principalId: principalId
    managedIdentityPrincipalId: managedIdentity.outputs.principalId
    name: keyVaultName
  }
}

// Deploy Application Insights
module appInsights 'core/monitoring/appInsights.bicep' = {
  name: 'appInsights-${uniqueString(subscription().id)}'
  scope: resourceGroup
  params: {
    location: location
    env: env
    logAnalyticsWorkspaceId: logAnalytics.outputs.workspaceId
    name: appInsightsName
    keyVaultName: keyVault.outputs.name
  }
}

// Deploy New Relic API Key to Key Vault
module newRelic 'core/monitoring/newrelic.bicep' = {
  name: 'newRelic-${uniqueString(subscription().id)}'
  scope: resourceGroup
  params: {
    keyVaultName: keyVault.outputs.name
    newrelicApiKey: newrelicApiKey
  }
}

// Deploy OpenAI Service
module openAI 'core/ai/openai.bicep' = {
  name: 'openAI-${uniqueString(subscription().id)}'
  scope: resourceGroup
  params: {
    location: location
    env: env
    customSubDomainName: openAiCustomSubDomainName
    managedIdentityId: managedIdentity.outputs.identityId
    managedIdentityPrincipalId: managedIdentity.outputs.principalId
    principalId: principalId
    name: openAiName
    openAiDeploymentCapacity: openAiDeploymentCapacity
    keyVaultName: keyVault.outputs.name
    clientId: managedIdentity.outputs.clientId
  }
}

// Deploy Computer Vision Service
module computerVision 'core/ai/computerVision.bicep' = {
  name: 'computerVision-${uniqueString(subscription().id)}'
  scope: resourceGroup
  params: {
    location: location
    env: env
    managedIdentityId: managedIdentity.outputs.identityId
    managedIdentityPrincipalId: managedIdentity.outputs.principalId
    principalId: principalId
    name: computerVisionName
    customSubDomainName: computerVisionCustomSubDomainName
    keyVaultName: keyVault.outputs.name
  }
}

// Deploy Container Registry
module containerRegistry 'core/registry/containerRegistry.bicep' = {
  name: 'containerRegistry-${uniqueString(subscription().id)}'
  scope: resourceGroup
  params: {
    location: location
    env: env
    //managedIdentityId: managedIdentity.outputs.identityId
    managedIdentityPrincipalId: managedIdentity.outputs.principalId
    logAnalyticsWorkspaceId: logAnalytics.outputs.workspaceId
    name: containerRegistryName
  }
}

// Deploy Container Apps Environment
module containerAppsEnvironment 'app/containerAppEnv.bicep' = {
  name: 'containerAppsEnvironment-${uniqueString(subscription().id)}'
  scope: resourceGroup
  params: {
    location: location
    env: env
    name: containerAppEnvName
    appInsightsName: appInsights.outputs.name
    logAnalyticsName: logAnalytics.outputs.name
  }
}

// Check if NLP Api container app already exists for future deploys
resource existingContainerAppNLPApi 'Microsoft.App/containerApps@2023-05-02-preview' existing = if (containerAppNLPApiExists) {
  scope: resourceGroup
  name: '${containerAppNLPApiName}-${env}'
}
// Deploy NLP Api Container App
module containerAppNLPApi 'app/nlp/containerApp-nlp-api.bicep' = {
  name: 'containerAppNLPApi-${uniqueString(subscription().id)}'
  scope: resourceGroup
  params: {
    location: location
    env: env
    containerAppsEnvironmentId: containerAppsEnvironment.outputs.id
    managedIdentityId: managedIdentity.outputs.identityId
    registryName: containerRegistry.outputs.name
    keyVaultName: keyVault.outputs.name
    openAiEndpoint: openAI.outputs.endpoint
    name: containerAppNLPApiName
    minReplicas: containerAppMinReplicas
    maxReplicas: containerAppMaxReplicas
    revisionsMode: containerAppRevisionMode
    imageName: containerAppNLPApiExists
      ? existingContainerAppNLPApi.properties.template.containers[0].image
      : 'mcr.microsoft.com/azuredocs/containerapps-helloworld:latest'
  }
}

// Check if Media Search Web container app already exists for future deploys
resource existingContainerAppMediaSearchWeb 'Microsoft.App/containerApps@2023-05-02-preview' existing = if (containerAppMediaSearchWebExists) {
  scope: resourceGroup
  name: '${containerAppMediaSearchWebName}-${env}'
}
// Deploy Media Search Web Container App
module containerAppMediaSearchWeb 'app/media/containerApp-media-search-web.bicep' = {
  name: 'containerAppMediaSearchWeb-${uniqueString(subscription().id)}'
  scope: resourceGroup
  params: {
    location: location
    env: env
    containerAppsEnvironmentId: containerAppsEnvironment.outputs.id
    managedIdentityId: managedIdentity.outputs.identityId
    registryName: containerRegistry.outputs.name
    keyVaultName: keyVault.outputs.name
    openAiEndpoint: openAI.outputs.endpoint
    name: containerAppMediaSearchWebName
    minReplicas: containerAppMinReplicas
    maxReplicas: containerAppMaxReplicas
    revisionsMode: containerAppRevisionMode
    imageName: containerAppMediaSearchWebExists
      ? existingContainerAppMediaSearchWeb.properties.template.containers[0].image
      : 'mcr.microsoft.com/azuredocs/containerapps-helloworld:latest'
  }
}

// Check if Listings Similarity Web container app already exists for future deploys
resource existingContainerListingsSimilarityWeb 'Microsoft.App/containerApps@2023-05-02-preview' existing = if (containerAppListingsSimilarityWebExists) {
  scope: resourceGroup
  name: '${containerAppListingsSimilarityWebName}-${env}'
}
// Deploy Media Search Web Container App
module containerAppListingsSimilarityWeb 'app/nlp/containerApp-listings-similarity-web.bicep' = {
  name: 'containerAppListingsSimilarityWeb-${uniqueString(subscription().id)}'
  scope: resourceGroup
  params: {
    location: location
    env: env
    containerAppsEnvironmentId: containerAppsEnvironment.outputs.id
    managedIdentityId: managedIdentity.outputs.identityId
    registryName: containerRegistry.outputs.name
    keyVaultName: keyVault.outputs.name
    openAiEndpoint: openAI.outputs.endpoint
    name: containerAppListingsSimilarityWebName
    minReplicas: containerAppMinReplicas
    maxReplicas: containerAppMaxReplicas
    revisionsMode: containerAppRevisionMode
    imageName: containerAppListingsSimilarityWebExists
      ? existingContainerListingsSimilarityWeb.properties.template.containers[0].image
      : 'mcr.microsoft.com/azuredocs/containerapps-helloworld:latest'
  }
}

// Check if Realtime Form Web container app already exists for future deploys
resource existingContainerRealtimeFormWeb 'Microsoft.App/containerApps@2023-05-02-preview' existing = if (containerAppRealtimeFormWebExists) {
  scope: resourceGroup
  name: '${containerAppRealtimeFormWebName}-${env}'
}
// Deploy Realtime Form Web Container App
module containerAppRealtimeFormWeb 'app/nlp/containerApp-realtime-form-web.bicep' = {
  name: 'containerAppRealtimeFormWeb-${uniqueString(subscription().id)}'
  scope: resourceGroup
  params: {
    location: location
    env: env
    containerAppsEnvironmentId: containerAppsEnvironment.outputs.id
    managedIdentityId: managedIdentity.outputs.identityId
    registryName: containerRegistry.outputs.name
    keyVaultName: keyVault.outputs.name
    openAiEndpoint: openAI.outputs.endpoint
    name: containerAppRealtimeFormWebName
    minReplicas: containerAppMinReplicas
    maxReplicas: containerAppMaxReplicas
    revisionsMode: containerAppRevisionMode
    imageName: containerAppRealtimeFormWebExists
      ? existingContainerRealtimeFormWeb.properties.template.containers[0].image
      : 'mcr.microsoft.com/azuredocs/containerapps-helloworld:latest'
  }
}

// Check if DB Chat Pro Web container app already exists for future deploys
resource existingContainerDBChatProWeb 'Microsoft.App/containerApps@2023-05-02-preview' existing = if (containerAppDBChatProWebExists) {
  scope: resourceGroup
  name: '${containerAppDBChatProWebName}-${env}'
}
// Deploy DB Chat Pro Web Container App
module containerAppDBChatProWeb 'app/nlp/containerApp-dbchatpro-web.bicep' = {
  name: 'containerAppDBChatProWeb-${uniqueString(subscription().id)}'
  scope: resourceGroup
  params: {
    location: location
    env: env
    containerAppsEnvironmentId: containerAppsEnvironment.outputs.id
    managedIdentityId: managedIdentity.outputs.identityId
    registryName: containerRegistry.outputs.name
    keyVaultName: keyVault.outputs.name
    openAiEndpoint: openAI.outputs.endpoint
    name: containerAppDBChatProWebName
    minReplicas: containerAppMinReplicas
    maxReplicas: containerAppMaxReplicas
    revisionsMode: containerAppRevisionMode
    imageName: containerAppDBChatProWebExists
      ? existingContainerDBChatProWeb.properties.template.containers[0].image
      : 'mcr.microsoft.com/azuredocs/containerapps-helloworld:latest'
  }
}

// Check if MCP Api container app already exists for future deploys
resource existingContainerAppMCPApi 'Microsoft.App/containerApps@2023-05-02-preview' existing = if (containerAppMCPApiExists) {
  scope: resourceGroup
  name: '${containerAppMCPApiName}-${env}'
}
// Deploy MCP Api Container App
module containerAppMCPApi 'app/mcp/containerApp-mcp-api.bicep' = {
  name: 'containerAppMCPApi-${uniqueString(subscription().id)}'
  scope: resourceGroup
  params: {
    location: location
    env: env
    containerAppsEnvironmentId: containerAppsEnvironment.outputs.id
    managedIdentityId: managedIdentity.outputs.identityId
    registryName: containerRegistry.outputs.name
    keyVaultName: keyVault.outputs.name
    openAiEndpoint: openAI.outputs.endpoint
    name: containerAppMCPApiName
    minReplicas: containerAppMinReplicas
    maxReplicas: containerAppMaxReplicas
    revisionsMode: containerAppRevisionMode
    imageName: containerAppMCPApiExists
      ? existingContainerAppMCPApi.properties.template.containers[0].image
      : 'mcr.microsoft.com/azuredocs/containerapps-helloworld:latest'
  }
}

output AZURE_RESOURCE_GROUP string = resourceGroup.name
output AZURE_CONTAINER_REGISTRY_ENDPOINT string = containerRegistry.outputs.loginServer
// output containerAppUrl string = containerApp.outputs.url
// output openAiEndpoint string = openAI.outputs.endpoint
