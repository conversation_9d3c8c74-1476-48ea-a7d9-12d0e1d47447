param location string
param env string
param containerAppsEnvironmentId string
param managedIdentityId string
param registryName string
param keyVaultName string
param openAiEndpoint string
param name string
@description('Minimum number of replicas. 2 for prod')
param minReplicas int
@description('Maximum number of replicas. 10 for prod')
param maxReplicas int
param revisionsMode string
param imageName string

@description('The container port')
param containerPort int = 8080

var containerAppName = '${name}-${env}'
var fullEnvName = env == 'prod' ? 'Production' : 'Development'

resource keyVault 'Microsoft.KeyVault/vaults@2023-07-01' existing = {
  name: keyVaultName
}

resource containerRegistry 'Microsoft.ContainerRegistry/registries@2023-11-01-preview' existing = {
  name: registryName
}

resource containerApp 'Microsoft.App/containerApps@2024-10-02-preview' = {
  name: containerAppName
  location: location
  tags: {
    'azd-service-name': 'listings-similarity-web'
  }
  identity: {
    type: 'UserAssigned'
    userAssignedIdentities: {
      '${managedIdentityId}': {}
    }
  }
  properties: {
    managedEnvironmentId: containerAppsEnvironmentId
    //workloadProfileName: 'Consumption'
    configuration: {
      activeRevisionsMode: revisionsMode
      ingress: {
        external: true
        targetPort: containerPort
        allowInsecure: false
        traffic: [
          {
            latestRevision: true
            weight: 100
          }
        ]
        transport: 'http'
      }
      registries: [
        {
          server: containerRegistry.properties.loginServer
          identity: managedIdentityId
        }
      ]
      secrets: [
        {
          name: 'app-insights-connection-string'
          keyVaultUrl: '${keyVault.properties.vaultUri}secrets/app-insights-connection-string'
          identity: managedIdentityId
        }
        {
          name: 'azure-client-id'
          keyVaultUrl: '${keyVault.properties.vaultUri}secrets/azure-client-id'
          identity: managedIdentityId
        }
        {
          name: 'azure-tenant-id'
          keyVaultUrl: '${keyVault.properties.vaultUri}secrets/azure-tenant-id'
          identity: managedIdentityId
        }
      ]
    }
    template: {
      containers: [
        {
          name: '${containerAppName}-main'
          image: imageName
          env: [
            {
              name: 'ASPNETCORE_ENVIRONMENT'
              value: fullEnvName
            }
            {
              name: 'ASPNETCORE_URLS'
              value: 'http://+:${containerPort}'
            }
            {
              name: 'APPLICATIONINSIGHTS_CONNECTION_STRING'
              secretRef: 'app-insights-connection-string'
            }
            {
              name: 'AZURE_OPENAI_ENDPOINT'
              value: openAiEndpoint
            }
            {
              name: 'AZURE_CLIENT_ID'
              secretRef: 'azure-client-id'
            }
            {
              name: 'AZURE_TENANT_ID'
              secretRef: 'azure-tenant-id'
            }
            {
              name: 'Logging__LogLevel__Default'
              value: 'Information'
            }
          ]
          resources: {
            cpu: json('0.5')
            memory: '1.0Gi'
          }
        }
      ]
      scale: {
        minReplicas: minReplicas
        maxReplicas: maxReplicas
        rules: [
          {
            name: 'http-rule'
            http: {
              metadata: {
                concurrentRequests: '100'
                scaleOnInFlight: 'true'
              }
            }
          }
          {
            name: 'cpu-rule'
            custom: {
              type: 'cpu'
              metadata: {
                type: 'Utilization'
                value: '70'
              }
            }
          }
          {
            name: 'memory-rule'
            custom: {
              type: 'memory'
              metadata: {
                type: 'Utilization'
                value: '70'
              }
            }
          }
        ]
      }
    }
  }
}

output id string = containerApp.id
output name string = containerApp.name
output url string = 'https://${containerApp.properties.configuration.ingress.fqdn}'
