{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#", "contentVersion": "*******", "parameters": {"location": {"value": "${AZURE_LOCATION=eastus2}"}, "principalId": {"value": "${AZURE_PRINCIPAL_ID}"}, "env": {"value": "${AZURE_ENV_NAME}"}, "logAnalyticsRetentionInDays": {"value": "${AZURE_LOG_ANALYTICS_RETENTION_IN_DAYS=30}"}, "logAnalyticsName": {"value": "log-ai-foundry"}, "appInsightsName": {"value": "appi-ai-foundry"}, "keyVaultName": {"value": "kv-ai-foundry"}, "openAiName": {"value": "ais-ai-foundry"}, "openAiCustomSubDomainName": {"value": "ai-foundry"}, "computerVisionName": {"value": "cv-media"}, "computerVisionCustomSubDomainName": {"value": "cv-media"}, "openAiDeploymentCapacity": {"value": "${APP_OPEN_AI_DEPLOYMENT_CAPACITY=50}"}, "containerRegistryName": {"value": "craifoundry"}, "containerAppEnvName": {"value": "cae-ai-foundry"}, "containerAppNLPApiName": {"value": "ca-nlp-api"}, "containerAppMediaSearchWebName": {"value": "ca-media-search-web"}, "containerAppListingsSimilarityWebName": {"value": "ca-listings-similarity-web"}, "containerAppRealtimeFormWebName": {"value": "ca-realtime-form-web"}, "containerAppDBChatProWebName": {"value": "ca-dbchatpro-web"}, "containerAppMCPApiName": {"value": "ca-mcp-api"}, "containerAppMinReplicas": {"value": "${APP_CONTAINER_APP_MIN_REPLICAS=1}"}, "containerAppMaxReplicas": {"value": "${APP_CONTAINER_APP_MAX_REPLICAS=5}"}, "containerAppRevisionMode": {"value": "${APP_CONTAINER_APP_REVISION_MODE=Single}"}, "containerAppNLPApiExists": {"value": "${SERVICE_NLP_API_RESOURCE_EXISTS=false}"}, "containerAppMediaSearchWebExists": {"value": "${SERVICE_MEDIA_SEARCH_WEB_RESOURCE_EXISTS=false}"}, "containerAppListingsSimilarityWebExists": {"value": "${SERVICE_LISTINGS_SIMILARITY_WEB_RESOURCE_EXISTS=false}"}, "containerAppRealtimeFormWebExists": {"value": "${SERVICE_REALTIME_FORM_WEB_RESOURCE_EXISTS=false}"}, "containerAppDBChatProWebExists": {"value": "${SERVICE_DBCHATPRO_WEB_RESOURCE_EXISTS=false}"}, "containerAppMCPApiExists": {"value": "${SERVICE_MCP_API_RESOURCE_EXISTS=false}"}, "newrelicApiKey": {"value": "${NEWRELIC_API_KEY}"}}}